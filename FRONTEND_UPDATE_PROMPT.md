# 🚀 MEDICONNECT FRONTEND UPDATE PROMPT

## 📋 PROJECT OVERVIEW

You are tasked with creating/updating a modern, responsive frontend for the **MediConnect Healthcare Management System**. The backend is a Spring Boot application running on `http://localhost:8083` with comprehensive REST API endpoints.

## 🎯 FRONTEND REQUIREMENTS

### **Technology Stack**
- **Framework**: React.js 18+ with TypeScript
- **Styling**: Tailwind CSS + Shadcn/ui components
- **State Management**: React Query (TanStack Query) + Zustand
- **Routing**: React Router v6
- **HTTP Client**: Axios
- **Authentication**: JWT tokens with automatic refresh
- **Form Handling**: React Hook Form + Zod validation
- **Icons**: Lucide React
- **Charts**: Recharts for dashboard analytics

### **Design Requirements**
- **Responsive Design**: Mobile-first approach
- **Theme**: Healthcare-focused color scheme (blues, greens, whites)
- **Accessibility**: WCAG 2.1 AA compliance
- **Performance**: Lazy loading, code splitting, optimized images
- **UX**: Intuitive navigation, loading states, error handling

## 🔐 AUTHENTICATION SYSTEM

### **Base URL**: `http://localhost:8083/api`

### **Auth Endpoints**

#### **1. Login**
```typescript
POST /auth/login
Content-Type: application/json

// Request Body
interface LoginRequest {
  email: string;
  password: string;
}

// Response
interface AuthResponse {
  token: string;
  email: string;
  role: 'ADMIN' | 'DOCTOR' | 'PATIENT' | 'CLINIC';
  userId: number;
  name: string;
  message: string;
}

// Example Request
{
  "email": "<EMAIL>",
  "password": "test123"
}

// Example Response
{
  "token": "eyJhbGciOiJIUzI1NiJ9...",
  "email": "<EMAIL>",
  "role": "ADMIN",
  "userId": 2,
  "name": "Test Admin",
  "message": "Authentication successful"
}
```

#### **2. Register**
```typescript
POST /auth/register
Content-Type: application/json

// Request Body (Patient)
interface PatientRegisterRequest {
  name: string;
  email: string;
  password: string;
  phoneNumber: string;
  address: string;
  dateOfBirth: string; // YYYY-MM-DD
  gender: 'MALE' | 'FEMALE' | 'OTHER';
  role: 'PATIENT';
  emergencyContactName: string;
  emergencyContactPhone: string;
  bloodGroup: string;
  allergies?: string;
  medicalHistory?: string;
}

// Request Body (Doctor)
interface DoctorRegisterRequest {
  name: string;
  email: string;
  password: string;
  phoneNumber: string;
  address: string;
  dateOfBirth: string;
  gender: 'MALE' | 'FEMALE' | 'OTHER';
  role: 'DOCTOR';
  medicalLicense: string;
  yearsOfExperience: number;
  qualification: string;
  consultationFee: number;
  bio: string;
}
```

#### **3. Token Management**
```typescript
POST /auth/logout
Authorization: Bearer {token}

POST /auth/validate
Authorization: Bearer {token}

POST /auth/refresh
Authorization: Bearer {token}
```

## 👑 ADMIN DASHBOARD

### **Admin Routes & Components**

#### **Dashboard Overview**
```typescript
GET /admin/reports/overview
Authorization: Bearer {adminToken}

// Response
interface SystemReport {
  totalUsers: number;
  totalDoctors: number;
  totalClinics: number;
  totalPatients: number;
  totalAppointments: number;
  pendingDoctorApprovals: number;
  pendingClinicApprovals: number;
  todayAppointments: number;
  completedAppointments: number;
  cancelledAppointments: number;
}
```

#### **User Management**
```typescript
GET /admin/users
Authorization: Bearer {adminToken}

// Response: UserDTO[]
interface UserDTO {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  address: string;
  dateOfBirth: string;
  gender: string;
  role: 'ADMIN' | 'DOCTOR' | 'PATIENT' | 'CLINIC' | 'CLINIC_STAFF';
  status: 'ACTIVE' | 'INACTIVE' | 'PENDING_APPROVAL' | 'SUSPENDED';
  createdAt: string;
  updatedAt: string;
  lastLogin: string;

  // Role-specific fields (populated based on user type)
  employeeId?: string; // For Admin and ClinicStaff
  department?: string; // For Admin
  accessLevel?: string; // For Admin
  medicalLicense?: string; // For Doctor
  specialtyName?: string; // For Doctor
  yearsOfExperience?: number; // For Doctor
  qualification?: string; // For Doctor
  doctorStatus?: string; // For Doctor
  clinicId?: number; // For Doctor and ClinicStaff
  clinicName?: string; // For Doctor and ClinicStaff
  consultationFee?: number; // For Doctor
  bio?: string; // For Doctor
  patientId?: string; // For Patient
  emergencyContactName?: string; // For Patient
  emergencyContactPhone?: string; // For Patient
  bloodGroup?: string; // For Patient
  allergies?: string; // For Patient
  medicalHistory?: string; // For Patient
  insuranceProvider?: string; // For Patient
  insuranceNumber?: string; // For Patient
  clinicLicenseNumber?: string; // For Clinic
  description?: string; // For Clinic
  clinicStatus?: string; // For Clinic
  operatingHours?: string; // For Clinic
  emergencyContact?: string; // For Clinic
  websiteUrl?: string; // For Clinic
  position?: string; // For ClinicStaff
  hireDate?: string; // For ClinicStaff
  salary?: number; // For ClinicStaff
  isActive?: boolean; // For ClinicStaff
}
```

#### **Role Change Management**
```typescript
// Option 1: Query Parameter Version
PUT /admin/users/{id}/change-role?newRole={ROLE}
Authorization: Bearer {adminToken}

// Option 2: Request Body Version (Recommended)
PUT /admin/users/{id}/role
Authorization: Bearer {adminToken}
Content-Type: application/json

// Request Body
interface RoleChangeRequest {
  newRole: 'ADMIN' | 'DOCTOR' | 'PATIENT' | 'CLINIC' | 'CLINIC_STAFF';
  reason?: string; // Optional reason for the role change
}

// Response
interface ApiResponse {
  success: boolean;
  message: string;
  data?: any;
}

// Example Request
{
  "newRole": "DOCTOR",
  "reason": "Medical professional access required"
}

// Example Success Response
{
  "success": true,
  "message": "User role changed successfully"
}

// Example Error Response
{
  "success": false,
  "message": "User already has the role: DOCTOR"
}

// Error Response - Invalid Role
{
  "success": false,
  "message": "Invalid role: INVALID_ROLE. Valid roles are: ADMIN, DOCTOR, PATIENT, CLINIC, CLINIC_STAFF"
}

// Error Response - User Not Found
{
  "success": false,
  "message": "User not found"
}

// Supported Role Transitions
// ✅ PATIENT ↔ DOCTOR (Doctor requires PENDING_APPROVAL status)
// ✅ PATIENT ↔ ADMIN (Admin gets ACTIVE status immediately)
// ✅ PATIENT ↔ CLINIC (Clinic requires PENDING_APPROVAL status)
// ✅ PATIENT ↔ CLINIC_STAFF (Staff gets ACTIVE status immediately)
// ✅ Any role ↔ Any other role (with appropriate status management)

// Status Management Rules:
// - DOCTOR/CLINIC roles: Set to PENDING_APPROVAL (requires admin approval)
// - PATIENT/ADMIN/CLINIC_STAFF roles: Set to ACTIVE (immediate access)
```

#### **Doctor Approvals**
```typescript
GET /admin/doctors/pending
Authorization: Bearer {adminToken}

PUT /admin/doctors/{id}/approve
Authorization: Bearer {adminToken}

PUT /admin/doctors/{id}/reject
Authorization: Bearer {adminToken}
```

## 👨‍⚕️ DOCTOR DASHBOARD

### **Doctor Routes & Components**

#### **Doctor Profile**
```typescript
GET /doctors/profile?doctorId={id}
Authorization: Bearer {doctorToken}

// Response
interface DoctorProfile {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  address: string;
  dateOfBirth: string;
  gender: string;
  medicalLicense: string;
  specialtyName: string;
  yearsOfExperience: number;
  qualification: string;
  doctorStatus: string;
  clinicId: number;
  clinicName: string;
  consultationFee: number;
  bio: string;
  createdAt: string;
  updatedAt: string;
}
```

#### **Patient Management**
```typescript
GET /doctors/patients?doctorId={id}
Authorization: Bearer {doctorToken}

// Response: Patient[]
interface Patient {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  patientId: string;
  bloodGroup: string;
  allergies: string;
  medicalHistory: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
}
```

#### **Diagnosis Creation**
```typescript
POST /doctors/patients/{patientId}/diagnosis
Authorization: Bearer {doctorToken}
Content-Type: application/json

// Request Body
interface DiagnosisRequest {
  doctorId: number;
  appointmentId: number;
  diagnosisCode: string;
  diagnosisDescription: string;
  symptoms: string;
  treatmentPlan: string;
  doctorNotes: string;
}
```

## 🤒 PATIENT DASHBOARD

### **Patient Routes & Components**

#### **Patient Profile**
```typescript
GET /patients/profile?patientId={id}
Authorization: Bearer {patientToken}

PUT /patients/profile?patientId={id}
Authorization: Bearer {patientToken}
Content-Type: application/json

// Request Body for Update
interface PatientUpdateRequest {
  name: string;
  phoneNumber: string;
  address: string;
  dateOfBirth: string;
  gender: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  bloodGroup: string;
  allergies: string;
  medicalHistory: string;
  insuranceProvider: string;
  insuranceNumber: string;
}
```

#### **Appointment Booking**
```typescript
POST /appointments
Authorization: Bearer {patientToken}
Content-Type: application/json

// Request Body
interface AppointmentRequest {
  patientId: number;
  doctorId: number;
  appointmentDate: string; // ISO datetime
  reason: string;
  notes?: string;
  durationMinutes?: number;
}

// Response
interface AppointmentResponse {
  id: number;
  patientId: number;
  patientName: string;
  doctorId: number;
  doctorName: string;
  doctorSpecialty: string;
  clinicId: number;
  clinicName: string;
  appointmentDate: string;
  status: 'SCHEDULED' | 'CONFIRMED' | 'COMPLETED' | 'CANCELLED';
  reason: string;
  notes: string;
  durationMinutes: number;
  createdAt: string;
}
```

#### **Doctor Search**
```typescript
GET /patients/search/doctors?specialty={specialty}
Authorization: Bearer {patientToken}

// Response: DoctorProfile[]
```

#### **Doctor Availability**
```typescript
GET /patients/doctors/{doctorId}/availability?date={YYYY-MM-DD}
Authorization: Bearer {patientToken}

// Response: string[] (time slots)
// Example: ["09:00", "09:30", "10:00", "10:30", ...]
```

## 🏥 CLINIC DASHBOARD

### **Clinic Routes & Components**

#### **Clinic Profile**
```typescript
GET /clinics/profile?clinicId={id}
Authorization: Bearer {clinicToken}

// Response
interface ClinicProfile {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  address: string;
  clinicName: string;
  description: string;
  clinicStatus: string;
  operatingHours: string;
  emergencyContact: string;
  websiteUrl: string;
  totalDoctors: number;
  totalStaff: number;
}
```

#### **Appointment Management**
```typescript
GET /clinics/appointments?clinicId={id}
Authorization: Bearer {clinicToken}

GET /clinics/appointments/today?clinicId={id}
Authorization: Bearer {clinicToken}

GET /clinics/appointments/upcoming?clinicId={id}
Authorization: Bearer {clinicToken}
```

## 📅 APPOINTMENT SYSTEM

### **Appointment Routes**

#### **Appointment CRUD**
```typescript
GET /appointments/{id}
Authorization: Bearer {token}

PUT /appointments/{id}/status?status={status}
Authorization: Bearer {token}

PUT /appointments/{id}/cancel
Authorization: Bearer {token}

GET /appointments/patient/{patientId}
Authorization: Bearer {token}

GET /appointments/doctor/{doctorId}
Authorization: Bearer {token}

GET /appointments/clinic/{clinicId}
Authorization: Bearer {token}
```

## 🎨 FRONTEND STRUCTURE

### **Folder Structure**
```
src/
├── components/
│   ├── ui/                 # Shadcn/ui components
│   ├── layout/            # Layout components
│   ├── forms/             # Form components
│   └── charts/            # Chart components
├── pages/
│   ├── auth/              # Login, Register
│   ├── admin/             # Admin dashboard
│   ├── doctor/            # Doctor dashboard
│   ├── patient/           # Patient dashboard
│   └── clinic/            # Clinic dashboard
├── hooks/                 # Custom React hooks
├── services/              # API services
├── stores/                # Zustand stores
├── types/                 # TypeScript interfaces
├── utils/                 # Utility functions
└── constants/             # App constants
```

### **Key Components to Build**

1. **Authentication**
   - LoginForm
   - RegisterForm (multi-step for different roles)
   - ProtectedRoute
   - RoleBasedRoute

2. **Dashboard Components**
   - StatCard
   - RecentActivity
   - AppointmentCalendar
   - PatientList
   - DoctorList

3. **Forms**
   - AppointmentBookingForm
   - ProfileUpdateForm
   - DiagnosisForm
   - AnnouncementForm
   - RoleChangeForm

4. **Data Tables**
   - UsersTable
   - AppointmentsTable
   - PatientsTable
   - DoctorsTable

5. **Modals & Dialogs**
   - AppointmentModal
   - ProfileModal
   - ConfirmationDialog

## 🔧 IMPLEMENTATION NOTES

### **Authentication Flow**
1. Store JWT token in localStorage/sessionStorage
2. Implement automatic token refresh
3. Redirect based on user role after login
4. Handle token expiration gracefully

### **Error Handling**
- Global error boundary
- API error interceptors
- User-friendly error messages
- Retry mechanisms for failed requests

### **Loading States**
- Skeleton loaders for data fetching
- Button loading states
- Page-level loading indicators
- Optimistic updates where appropriate

### **Real-time Features** (Future Enhancement)
- WebSocket connection for real-time notifications
- Live appointment updates
- Chat system between doctors and patients

## 🚀 GETTING STARTED

1. **Setup Project**
   ```bash
   npx create-react-app mediconnect-frontend --template typescript
   cd mediconnect-frontend
   npm install @tanstack/react-query zustand react-router-dom axios
   npm install tailwindcss @shadcn/ui lucide-react recharts
   npm install react-hook-form @hookform/resolvers zod
   ```

2. **Configure Environment**
   ```env
   REACT_APP_API_BASE_URL=http://localhost:8083/api
   REACT_APP_JWT_SECRET=your-jwt-secret
   ```

3. **Test Credentials**
   ```typescript
   // Admin
   { email: "<EMAIL>", password: "test123" }

   // Patient
   { email: "<EMAIL>", password: "test123" }

   // Doctor
   { email: "<EMAIL>", password: "doctor123" }

   // Clinic
   { email: "<EMAIL>", password: "clinic123" }
   ```

## � ROLE CHANGE API ENDPOINTS

### **Primary Role Change Endpoints**

#### **1. Change User Role (Request Body - Recommended)**
```http
PUT /api/admin/users/{userId}/role
Authorization: Bearer {adminToken}
Content-Type: application/json

Request Body:
{
  "newRole": "DOCTOR",
  "reason": "Medical professional access required"
}

Response:
{
  "success": true,
  "message": "User role changed successfully",
  "data": null
}
```

#### **2. Change User Role (Query Parameter)**
```http
PUT /api/admin/users/{userId}/change-role?newRole={ROLE}
Authorization: Bearer {adminToken}

Response:
{
  "success": true,
  "message": "User role changed successfully",
  "data": null
}
```

### **Supporting Admin Endpoints**

#### **3. Get All Users**
```http
GET /api/admin/users
Authorization: Bearer {adminToken}

Response: UserDTO[]
```

#### **4. Get Single User**
```http
GET /api/admin/users/{userId}
Authorization: Bearer {adminToken}

Response: UserDTO
```

#### **5. Activate User**
```http
PUT /api/admin/users/{userId}/activate
Authorization: Bearer {adminToken}

Response:
{
  "success": true,
  "message": "User activated successfully"
}
```

#### **6. Deactivate User**
```http
PUT /api/admin/users/{userId}/deactivate
Authorization: Bearer {adminToken}

Response:
{
  "success": true,
  "message": "User deactivated successfully"
}
```

### **Error Responses**

#### **Invalid Role**
```json
{
  "success": false,
  "message": "Invalid role: INVALID_ROLE. Valid roles are: ADMIN, DOCTOR, PATIENT, CLINIC, CLINIC_STAFF"
}
```

#### **Same Role**
```json
{
  "success": false,
  "message": "User already has the role: DOCTOR"
}
```

#### **User Not Found**
```json
{
  "success": false,
  "message": "User not found"
}
```

#### **Unauthorized**
```json
{
  "success": false,
  "message": "Access denied"
}
```

### **Frontend Integration Flow**

1. **Login**: `POST /api/auth/login` → Get admin token
2. **Load Users**: `GET /api/admin/users` → Display users table
3. **Change Role**: `PUT /api/admin/users/{id}/role` → Update user role
4. **Refresh**: `GET /api/admin/users` → Reload updated data
5. **Verify**: `GET /api/admin/users/{id}` → Confirm role change

## �📊 ADDITIONAL API ENDPOINTS

### **Follow-up Management**
```typescript
POST /doctors/followups
Authorization: Bearer {doctorToken}
Content-Type: application/json

// Request Body
interface FollowUpRequest {
  patientId: number;
  doctorId: number;
  scheduledDate: string; // ISO datetime
  reason: string;
  notes: string;
}

GET /doctors/followups?doctorId={id}
Authorization: Bearer {doctorToken}

PUT /doctors/followups/{id}
Authorization: Bearer {doctorToken}

DELETE /doctors/followups/{id}
Authorization: Bearer {doctorToken}
```

### **Medical History**
```typescript
GET /patients/medical-history?patientId={id}
Authorization: Bearer {patientToken}

GET /patients/diagnoses?patientId={id}
Authorization: Bearer {patientToken}

GET /patients/prescriptions?patientId={id}
Authorization: Bearer {patientToken}
```

### **Clinic Staff Management**
```typescript
GET /clinics/staff?clinicId={id}
Authorization: Bearer {clinicToken}

POST /clinics/staff/doctors?clinicId={id}
Authorization: Bearer {clinicToken}

DELETE /clinics/staff/doctors/{doctorId}?clinicId={id}
Authorization: Bearer {clinicToken}
```

### **Announcements**
```typescript
GET /clinics/announcements?clinicId={id}
Authorization: Bearer {clinicToken}

POST /clinics/announcements?clinicId={id}
Authorization: Bearer {clinicToken}
Content-Type: application/json

// Request Body
interface AnnouncementRequest {
  title: string;
  content: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  startDate: string; // ISO datetime
  endDate: string; // ISO datetime
}

PUT /clinics/announcements/{id}?clinicId={id}
Authorization: Bearer {clinicToken}

DELETE /clinics/announcements/{id}?clinicId={id}
Authorization: Bearer {clinicToken}
```

## 🎨 DETAILED UI/UX SPECIFICATIONS

### **Color Palette**
```css
:root {
  /* Primary Colors */
  --primary-blue: #2563eb;
  --primary-green: #059669;
  --primary-teal: #0d9488;

  /* Secondary Colors */
  --secondary-blue: #dbeafe;
  --secondary-green: #d1fae5;
  --secondary-gray: #f8fafc;

  /* Status Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Text Colors */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
}
```

### **Component Specifications**

#### **Dashboard Cards**
```typescript
interface StatCardProps {
  title: string;
  value: number | string;
  icon: LucideIcon;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color: 'blue' | 'green' | 'yellow' | 'red';
}
```

#### **Data Tables**
```typescript
interface TableColumn<T> {
  key: keyof T;
  header: string;
  render?: (value: any, row: T) => React.ReactNode;
  sortable?: boolean;
  filterable?: boolean;
}

interface DataTableProps<T> {
  data: T[];
  columns: TableColumn<T>[];
  pagination?: boolean;
  searchable?: boolean;
  actions?: (row: T) => React.ReactNode;
}
```

#### **Form Components**
```typescript
interface FormFieldProps {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'select' | 'textarea' | 'date' | 'time';
  placeholder?: string;
  required?: boolean;
  options?: { value: string; label: string; }[];
  validation?: ZodSchema;
}
```

## 🔄 STATE MANAGEMENT

### **Zustand Stores**

#### **Auth Store**
```typescript
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  updateProfile: (data: any) => Promise<void>;
}
```

#### **Appointment Store**
```typescript
interface AppointmentState {
  appointments: Appointment[];
  selectedAppointment: Appointment | null;
  isLoading: boolean;
  fetchAppointments: (filters?: any) => Promise<void>;
  createAppointment: (data: AppointmentRequest) => Promise<void>;
  updateAppointment: (id: number, data: any) => Promise<void>;
  cancelAppointment: (id: number) => Promise<void>;
}
```

### **React Query Keys**
```typescript
export const queryKeys = {
  auth: ['auth'] as const,
  users: ['users'] as const,
  doctors: ['doctors'] as const,
  patients: ['patients'] as const,
  clinics: ['clinics'] as const,
  appointments: ['appointments'] as const,
  diagnoses: ['diagnoses'] as const,
  followUps: ['followUps'] as const,
  announcements: ['announcements'] as const,

  // Specific queries
  userProfile: (id: number) => ['users', 'profile', id] as const,
  doctorPatients: (doctorId: number) => ['doctors', doctorId, 'patients'] as const,
  patientAppointments: (patientId: number) => ['patients', patientId, 'appointments'] as const,
  doctorAvailability: (doctorId: number, date: string) => ['doctors', doctorId, 'availability', date] as const,
};
```

### **Role Change React Hook**
```typescript
import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ApiService } from '@/services/api';
import { useToast } from '@/hooks/use-toast';

interface UseRoleChangeProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const useRoleChange = ({ onSuccess, onError }: UseRoleChangeProps = {}) => {
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const apiService = new ApiService();

  const roleChangeMutation = useMutation({
    mutationFn: async ({ userId, newRole, reason }: {
      userId: number;
      newRole: string;
      reason?: string;
    }) => {
      return apiService.changeUserRole(userId, newRole, reason);
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch users data
      queryClient.invalidateQueries({ queryKey: queryKeys.users });
      queryClient.invalidateQueries({ queryKey: queryKeys.userProfile(variables.userId) });

      toast({
        title: 'Success',
        description: `User role changed to ${variables.newRole} successfully`,
        variant: 'default',
      });

      onSuccess?.();
    },
    onError: (error: any, variables) => {
      const errorMessage = error?.response?.data?.message || error.message || 'Failed to change user role';

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });

      onError?.(errorMessage);
    },
  });

  const changeRole = async (userId: number, newRole: string, reason?: string) => {
    try {
      setIsLoading(true);
      await roleChangeMutation.mutateAsync({ userId, newRole, reason });
    } catch (error) {
      // Error is handled in onError callback
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    changeRole,
    isLoading: isLoading || roleChangeMutation.isPending,
    error: roleChangeMutation.error,
    isSuccess: roleChangeMutation.isSuccess,
    reset: roleChangeMutation.reset,
  };
};

// Usage Example
const RoleChangeComponent = () => {
  const { changeRole, isLoading } = useRoleChange({
    onSuccess: () => {
      console.log('Role changed successfully');
    },
    onError: (error) => {
      console.error('Role change failed:', error);
    }
  });

  const handleRoleChange = async () => {
    try {
      await changeRole(20, 'DOCTOR', 'Medical professional access required');
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  return (
    <button
      onClick={handleRoleChange}
      disabled={isLoading}
    >
      {isLoading ? 'Changing Role...' : 'Change Role'}
    </button>
  );
};
```

## 🧪 TESTING STRATEGY

### **Test Credentials**
```typescript
export const testCredentials = {
  admin: { email: "<EMAIL>", password: "test123" },
  patient: { email: "<EMAIL>", password: "test123" },
  doctor: { email: "<EMAIL>", password: "doctor123" },
  clinic: { email: "<EMAIL>", password: "clinic123" }
};
```

### **Role Change API Testing**
```typescript
// Test Users for Role Change
export const testUsers = [
  {
    id: 20,
    name: "Isse Hassan",
    email: "<EMAIL>",
    currentRole: "PATIENT"
  },
  {
    id: 24,
    name: "Farxiya Xasan",
    email: "<EMAIL>",
    currentRole: "DOCTOR"
  }
];

// Example Test Calls
// 1. Login as Admin
fetch('http://localhost:8083/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email: "<EMAIL>",
    password: "test123"
  })
});

// 2. Change Role (Request Body Method - Recommended)
fetch('http://localhost:8083/api/admin/users/20/role', {
  method: 'PUT',
  headers: {
    'Authorization': 'Bearer {TOKEN}',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    newRole: "DOCTOR",
    reason: "Medical professional access required"
  })
});

// 3. Change Role (Query Parameter Method)
fetch('http://localhost:8083/api/admin/users/20/change-role?newRole=PATIENT', {
  method: 'PUT',
  headers: {
    'Authorization': 'Bearer {TOKEN}'
  }
});

// 4. Get All Users
fetch('http://localhost:8083/api/admin/users', {
  headers: {
    'Authorization': 'Bearer {TOKEN}'
  }
});

// 5. Get Single User
fetch('http://localhost:8083/api/admin/users/20', {
  headers: {
    'Authorization': 'Bearer {TOKEN}'
  }
});
```

### **API Testing**
```typescript
// Example API service
export class ApiService {
  private baseURL = process.env.REACT_APP_API_BASE_URL;
  private axiosInstance: AxiosInstance;

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
    });

    // Request interceptor for auth token
    this.axiosInstance.interceptors.request.use((config) => {
      const token = localStorage.getItem('authToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });

    // Response interceptor for error handling
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Handle token expiration
          localStorage.removeItem('authToken');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth methods
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await this.axiosInstance.post('/auth/login', credentials);
    return response.data;
  }

  async register(data: RegisterRequest): Promise<AuthResponse> {
    const response = await this.axiosInstance.post('/auth/register', data);
    return response.data;
  }

  // Admin methods
  async getAllUsers(): Promise<UserDTO[]> {
    const response = await this.axiosInstance.get('/admin/users');
    return response.data;
  }

  async getUser(userId: number): Promise<UserDTO> {
    const response = await this.axiosInstance.get(`/admin/users/${userId}`);
    return response.data;
  }

  async changeUserRole(userId: number, newRole: string, reason?: string): Promise<ApiResponse> {
    const response = await this.axiosInstance.put(`/admin/users/${userId}/role`, {
      newRole,
      reason
    });
    return response.data;
  }

  async changeUserRoleQuery(userId: number, newRole: string): Promise<ApiResponse> {
    const response = await this.axiosInstance.put(`/admin/users/${userId}/change-role?newRole=${newRole}`);
    return response.data;
  }

  async activateUser(userId: number): Promise<ApiResponse> {
    const response = await this.axiosInstance.put(`/admin/users/${userId}/activate`);
    return response.data;
  }

  async deactivateUser(userId: number): Promise<ApiResponse> {
    const response = await this.axiosInstance.put(`/admin/users/${userId}/deactivate`);
    return response.data;
  }

  // Appointment methods
  async createAppointment(data: AppointmentRequest): Promise<AppointmentResponse> {
    const response = await this.axiosInstance.post('/appointments', data);
    return response.data;
  }

  async getAppointments(filters?: any): Promise<AppointmentResponse[]> {
    const response = await this.axiosInstance.get('/appointments', { params: filters });
    return response.data;
  }
}
```

## 📱 RESPONSIVE DESIGN

### **Breakpoints**
```css
/* Mobile First Approach */
.container {
  @apply px-4 mx-auto;
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
  .container { @apply max-w-sm; }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .container { @apply max-w-md px-6; }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  .container { @apply max-w-lg px-8; }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .container { @apply max-w-7xl; }
}
```

### **Mobile Navigation**
- Collapsible sidebar for mobile
- Bottom navigation for quick access
- Swipe gestures for appointment calendar
- Touch-friendly button sizes (min 44px)

## 🚀 PERFORMANCE OPTIMIZATION

### **Code Splitting**
```typescript
// Lazy load pages
const AdminDashboard = lazy(() => import('./pages/admin/Dashboard'));
const DoctorDashboard = lazy(() => import('./pages/doctor/Dashboard'));
const PatientDashboard = lazy(() => import('./pages/patient/Dashboard'));
const ClinicDashboard = lazy(() => import('./pages/clinic/Dashboard'));
```

### **Image Optimization**
- Use WebP format with fallbacks
- Implement lazy loading for images
- Optimize avatar images with proper sizing
- Use placeholder images during loading

### **Bundle Optimization**
- Tree shaking for unused code
- Minimize bundle size with webpack-bundle-analyzer
- Use dynamic imports for heavy libraries
- Implement service worker for caching

## 🔒 SECURITY CONSIDERATIONS

### **Input Validation**
```typescript
// Zod schemas for form validation
export const appointmentSchema = z.object({
  patientId: z.number().positive(),
  doctorId: z.number().positive(),
  appointmentDate: z.string().datetime(),
  reason: z.string().min(1).max(500),
  notes: z.string().max(1000).optional(),
  durationMinutes: z.number().min(15).max(180).optional(),
});
```

### **XSS Prevention**
- Sanitize all user inputs
- Use dangerouslySetInnerHTML sparingly
- Validate data from API responses
- Implement Content Security Policy

### **Authentication Security**
- Secure token storage
- Automatic logout on inactivity
- HTTPS enforcement
- Rate limiting for login attempts

Build a modern, intuitive healthcare management frontend that provides seamless user experience across all roles! 🏥✨

## 📋 IMPLEMENTATION CHECKLIST

### **Phase 1: Core Setup**
- [ ] Project setup with TypeScript + Tailwind
- [ ] Authentication system
- [ ] Routing configuration
- [ ] API service layer
- [ ] State management setup

### **Phase 2: User Interfaces**
- [ ] Login/Register forms
- [ ] Admin dashboard
- [ ] Doctor dashboard
- [ ] Patient dashboard
- [ ] Clinic dashboard

### **Phase 3: Features**
- [ ] Appointment booking system
- [ ] Profile management
- [ ] Medical records
- [ ] Notifications
- [ ] Search functionality

### **Phase 4: Polish**
- [ ] Responsive design
- [ ] Error handling
- [ ] Loading states
- [ ] Performance optimization
- [ ] Testing coverage

Start with Phase 1 and build incrementally. Focus on user experience and ensure all API integrations work seamlessly! 🚀
