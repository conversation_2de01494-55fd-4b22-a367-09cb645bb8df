// Frontend JavaScript for Admin Settings Page
// This code should be added to your /admin/settings page

class AdminSettings {
    constructor() {
        this.settings = {
            twoFactorAuthEnabled: false,
            strongPasswordPolicyEnabled: true,
            sessionTimeoutMinutes: 30,
            maxLoginAttempts: 5,
            dataEncryptionEnabled: true,
            auditLoggingEnabled: true
        };
        this.token = localStorage.getItem('authToken');
        this.init();
    }

    init() {
        this.loadSettings();
        this.bindEvents();
    }

    async loadSettings() {
        try {
            const response = await fetch('/api/admin/settings/security', {
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.settings = data;
                this.updateUI();
                this.showMessage('Settings loaded successfully', 'success');
            } else {
                this.showMessage('Failed to load settings', 'error');
            }
        } catch (error) {
            console.error('Error loading settings:', error);
            this.showMessage('Error loading settings', 'error');
        }
    }

    async saveSettings() {
        try {
            this.showLoading(true);
            
            const response = await fetch('/api/admin/settings/security', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(this.settings)
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.settings = result.data;
                this.updateUI();
                this.showMessage('Settings saved successfully!', 'success');
            } else {
                this.showMessage(result.message || 'Failed to save settings', 'error');
            }
        } catch (error) {
            console.error('Error saving settings:', error);
            this.showMessage('Error saving settings', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    bindEvents() {
        // Save Settings Button
        const saveButton = document.getElementById('saveSettingsBtn');
        if (saveButton) {
            saveButton.addEventListener('click', () => this.saveSettings());
        }

        // Two-Factor Authentication Toggle
        const twoFactorToggle = document.getElementById('twoFactorAuth');
        if (twoFactorToggle) {
            twoFactorToggle.addEventListener('change', (e) => {
                this.settings.twoFactorAuthEnabled = e.target.checked;
            });
        }

        // Strong Password Policy Toggle
        const passwordPolicyToggle = document.getElementById('strongPasswordPolicy');
        if (passwordPolicyToggle) {
            passwordPolicyToggle.addEventListener('change', (e) => {
                this.settings.strongPasswordPolicyEnabled = e.target.checked;
            });
        }

        // Session Timeout Input
        const sessionTimeoutInput = document.getElementById('sessionTimeout');
        if (sessionTimeoutInput) {
            sessionTimeoutInput.addEventListener('change', (e) => {
                this.settings.sessionTimeoutMinutes = parseInt(e.target.value);
            });
        }

        // Max Login Attempts Input
        const maxAttemptsInput = document.getElementById('maxLoginAttempts');
        if (maxAttemptsInput) {
            maxAttemptsInput.addEventListener('change', (e) => {
                this.settings.maxLoginAttempts = parseInt(e.target.value);
            });
        }

        // Data Encryption Toggle
        const dataEncryptionToggle = document.getElementById('dataEncryption');
        if (dataEncryptionToggle) {
            dataEncryptionToggle.addEventListener('change', (e) => {
                this.settings.dataEncryptionEnabled = e.target.checked;
            });
        }

        // Audit Logging Toggle
        const auditLoggingToggle = document.getElementById('auditLogging');
        if (auditLoggingToggle) {
            auditLoggingToggle.addEventListener('change', (e) => {
                this.settings.auditLoggingEnabled = e.target.checked;
            });
        }

        // Test Email Button
        const testEmailBtn = document.getElementById('testEmailBtn');
        if (testEmailBtn) {
            testEmailBtn.addEventListener('click', () => this.testEmail());
        }

        // Reset Button
        const resetBtn = document.getElementById('resetBtn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.loadSettings());
        }
    }

    updateUI() {
        // Update toggles
        const twoFactorToggle = document.getElementById('twoFactorAuth');
        if (twoFactorToggle) {
            twoFactorToggle.checked = this.settings.twoFactorAuthEnabled;
        }

        const passwordPolicyToggle = document.getElementById('strongPasswordPolicy');
        if (passwordPolicyToggle) {
            passwordPolicyToggle.checked = this.settings.strongPasswordPolicyEnabled;
        }

        const dataEncryptionToggle = document.getElementById('dataEncryption');
        if (dataEncryptionToggle) {
            dataEncryptionToggle.checked = this.settings.dataEncryptionEnabled;
        }

        const auditLoggingToggle = document.getElementById('auditLogging');
        if (auditLoggingToggle) {
            auditLoggingToggle.checked = this.settings.auditLoggingEnabled;
        }

        // Update inputs
        const sessionTimeoutInput = document.getElementById('sessionTimeout');
        if (sessionTimeoutInput) {
            sessionTimeoutInput.value = this.settings.sessionTimeoutMinutes;
        }

        const maxAttemptsInput = document.getElementById('maxLoginAttempts');
        if (maxAttemptsInput) {
            maxAttemptsInput.value = this.settings.maxLoginAttempts;
        }

        // Update security status if available
        if (this.settings.securityLevel) {
            this.updateSecurityStatus();
        }
    }

    updateSecurityStatus() {
        const statusElement = document.getElementById('securityStatus');
        if (statusElement) {
            const level = this.settings.securityLevel;
            const score = this.settings.securityScore;
            
            statusElement.innerHTML = `
                <div class="security-status ${level.toLowerCase()}">
                    <span class="security-level">${level} Security</span>
                    <span class="security-score">Score: ${score}/100</span>
                </div>
            `;
        }
    }

    async testEmail() {
        try {
            const response = await fetch('/api/admin/settings/test-email', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (response.ok && result.success) {
                this.showMessage('Test email sent successfully!', 'success');
            } else {
                this.showMessage(result.message || 'Failed to send test email', 'error');
            }
        } catch (error) {
            console.error('Error testing email:', error);
            this.showMessage('Error testing email configuration', 'error');
        }
    }

    showMessage(message, type) {
        // Remove existing messages
        const existingMessages = document.querySelectorAll('.settings-message');
        existingMessages.forEach(msg => msg.remove());

        // Create new message
        const messageDiv = document.createElement('div');
        messageDiv.className = `settings-message ${type}`;
        messageDiv.textContent = message;

        // Add styles
        messageDiv.style.cssText = `
            padding: 12px 16px;
            margin: 16px 0;
            border-radius: 4px;
            font-weight: 500;
            ${type === 'success' ? 
                'background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb;' : 
                'background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;'
            }
        `;

        // Insert message at the top of the settings form
        const settingsForm = document.querySelector('.settings-form') || document.querySelector('.admin-settings');
        if (settingsForm) {
            settingsForm.insertBefore(messageDiv, settingsForm.firstChild);
        }

        // Auto-remove after 5 seconds
        setTimeout(() => {
            messageDiv.remove();
        }, 5000);
    }

    showLoading(show) {
        const saveButton = document.getElementById('saveSettingsBtn');
        if (saveButton) {
            if (show) {
                saveButton.disabled = true;
                saveButton.textContent = 'Saving...';
            } else {
                saveButton.disabled = false;
                saveButton.textContent = 'Save Settings';
            }
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AdminSettings();
});

// CSS Styles (add to your stylesheet)
const styles = `
.security-status {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
}

.security-status.high {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.security-status.medium {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.security-status.low {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.security-level {
    font-weight: 600;
}

.security-score {
    font-size: 12px;
    opacity: 0.8;
}

.settings-message {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
`;

// Add styles to head
const styleSheet = document.createElement('style');
styleSheet.textContent = styles;
document.head.appendChild(styleSheet);
