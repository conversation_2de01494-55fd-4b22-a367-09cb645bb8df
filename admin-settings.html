<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Settings - MediConnect</title>
    <style>
        .admin-settings {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .settings-header {
            margin-bottom: 30px;
        }

        .settings-header h1 {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 8px 0;
        }

        .settings-header p {
            color: #6b7280;
            margin: 0;
        }

        .settings-section {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 8px 0;
        }

        .section-description {
            color: #6b7280;
            font-size: 14px;
            margin: 0 0 20px 0;
        }

        .subsection-title {
            font-size: 16px;
            font-weight: 500;
            color: #374151;
            margin: 24px 0 12px 0;
        }

        .subsection-description {
            color: #6b7280;
            font-size: 14px;
            margin: 0 0 16px 0;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-info {
            flex: 1;
        }

        .setting-label {
            font-weight: 500;
            color: #374151;
            margin: 0 0 4px 0;
        }

        .setting-description {
            font-size: 13px;
            color: #6b7280;
            margin: 0;
        }

        .setting-control {
            margin-left: 16px;
        }

        /* Toggle Switch */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #d1d5db;
            transition: .4s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: #3b82f6;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(20px);
        }

        /* Number Input */
        .number-input {
            width: 80px;
            padding: 6px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            text-align: center;
        }

        .number-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Buttons */
        .button-group {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #e5e7eb;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border: 1px solid;
            transition: all 0.2s;
        }

        .btn-secondary {
            background: white;
            color: #374151;
            border-color: #d1d5db;
        }

        .btn-secondary:hover {
            background: #f9fafb;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* Security Status */
        #securityStatus {
            margin-bottom: 16px;
        }
    </style>
</head>
<body>
    <div class="admin-settings">
        <div class="settings-header">
            <h1>Admin Settings</h1>
            <p>Manage system security and access controls</p>
        </div>

        <!-- Security Status Display -->
        <div id="securityStatus"></div>

        <!-- Security Settings Section -->
        <div class="settings-section">
            <h2 class="section-title">Security Settings</h2>
            <p class="section-description">Manage system security and access controls</p>

            <!-- Authentication Settings -->
            <h3 class="subsection-title">Authentication Settings</h3>
            <p class="subsection-description">Configure user authentication and access policies</p>

            <div class="setting-item">
                <div class="setting-info">
                    <div class="setting-label">Two-Factor Authentication</div>
                    <div class="setting-description">Require 2FA for all admin accounts</div>
                </div>
                <div class="setting-control">
                    <label class="toggle-switch">
                        <input type="checkbox" id="twoFactorAuth">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
            </div>

            <div class="setting-item">
                <div class="setting-info">
                    <div class="setting-label">Strong Password Policy</div>
                    <div class="setting-description">Enforce complex password requirements</div>
                </div>
                <div class="setting-control">
                    <label class="toggle-switch">
                        <input type="checkbox" id="strongPasswordPolicy">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
            </div>

            <div class="setting-item">
                <div class="setting-info">
                    <div class="setting-label">Session Timeout (minutes)</div>
                    <div class="setting-description">Automatic logout after inactivity</div>
                </div>
                <div class="setting-control">
                    <input type="number" id="sessionTimeout" class="number-input" min="5" max="480" value="30">
                </div>
            </div>

            <div class="setting-item">
                <div class="setting-info">
                    <div class="setting-label">Max Login Attempts</div>
                    <div class="setting-description">Account lockout threshold</div>
                </div>
                <div class="setting-control">
                    <input type="number" id="maxLoginAttempts" class="number-input" min="3" max="10" value="5">
                </div>
            </div>

            <!-- Data Security -->
            <h3 class="subsection-title">Data Security</h3>
            <p class="subsection-description">Configure data protection and monitoring</p>

            <div class="setting-item">
                <div class="setting-info">
                    <div class="setting-label">Data Encryption</div>
                    <div class="setting-description">Encrypt sensitive data at rest</div>
                </div>
                <div class="setting-control">
                    <label class="toggle-switch">
                        <input type="checkbox" id="dataEncryption">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
            </div>

            <div class="setting-item">
                <div class="setting-info">
                    <div class="setting-label">Audit Logging</div>
                    <div class="setting-description">Log all admin and system actions</div>
                </div>
                <div class="setting-control">
                    <label class="toggle-switch">
                        <input type="checkbox" id="auditLogging">
                        <span class="toggle-slider"></span>
                    </label>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="button-group">
                <button type="button" id="testEmailBtn" class="btn btn-secondary">Test Email</button>
                <button type="button" id="resetBtn" class="btn btn-secondary">Reset</button>
                <button type="button" id="saveSettingsBtn" class="btn btn-primary">Save Settings</button>
            </div>
        </div>
    </div>

    <!-- Include the JavaScript -->
    <script src="admin-settings-frontend.js"></script>
</body>
</html>
