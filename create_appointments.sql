-- Create comprehensive appointment data for MediConnect
-- This script creates appointments with various statuses and dates

-- First, let's check if we have the necessary data
-- SELECT COUNT(*) as patient_count FROM patients;
-- SELECT COUNT(*) as doctor_count FROM doctors;
-- SELECT COUNT(*) as clinic_count FROM clinics;

-- Clear existing appointments (optional) - Handle foreign key constraints
SET FOREIGN_KEY_CHECKS = 0;
DELETE FROM diagnoses WHERE appointment_id IS NOT NULL;
DELETE FROM appointments;
SET FOREIGN_KEY_CHECKS = 1;

-- Insert comprehensive appointment data
INSERT IGNORE INTO appointments (patient_id, doctor_id, clinic_id, appointment_date, status, reason, duration_minutes, created_at, updated_at, notes) VALUES

-- Past completed appointments (November 2024)
(4, 3, 2, '2024-11-01 09:00:00', 'COMPLETED', 'Regular checkup', 30, '2024-10-28 10:00:00', '2024-11-01 09:30:00', 'Patient reports feeling well overall'),
(4, 3, 2, '2024-11-05 14:30:00', 'COMPLETED', 'Follow-up consultation', 45, '2024-11-02 11:00:00', '2024-11-05 15:15:00', 'Blood pressure stable, continue medication'),
(4, 3, 2, '2024-11-08 10:15:00', 'COMPLETED', 'Blood pressure monitoring', 30, '2024-11-05 16:00:00', '2024-11-08 10:45:00', 'Slight improvement noted'),
(4, 3, 2, '2024-11-12 11:00:00', 'COMPLETED', 'Vaccination', 20, '2024-11-09 10:00:00', '2024-11-12 11:20:00', 'Flu vaccination administered'),
(4, 3, 2, '2024-11-15 15:30:00', 'COMPLETED', 'Allergy consultation', 40, '2024-11-12 14:00:00', '2024-11-15 16:10:00', 'Prescribed antihistamines'),
(4, 3, 2, '2024-11-19 09:45:00', 'COMPLETED', 'Diabetes management', 45, '2024-11-16 12:00:00', '2024-11-19 10:30:00', 'Blood sugar levels improving'),
(4, 3, 2, '2024-11-22 13:15:00', 'COMPLETED', 'Routine physical exam', 60, '2024-11-19 09:00:00', '2024-11-22 14:15:00', 'Annual physical completed'),
(4, 3, 2, '2024-11-26 10:30:00', 'COMPLETED', 'Medication review', 30, '2024-11-23 15:00:00', '2024-11-26 11:00:00', 'Adjusted dosage for blood pressure medication'),
(4, 3, 2, '2024-11-29 14:00:00', 'COMPLETED', 'Lab results discussion', 30, '2024-11-26 11:00:00', '2024-11-29 14:30:00', 'All lab results within normal range'),

-- Past cancelled appointments
(4, 3, 2, '2024-11-18 13:00:00', 'CANCELLED', 'Skin examination', 30, '2024-11-15 12:00:00', '2024-11-18 08:00:00', 'Patient cancelled due to illness'),
(4, 3, 2, '2024-11-25 16:00:00', 'CANCELLED', 'Chest pain evaluation', 45, '2024-11-22 09:00:00', '2024-11-25 10:00:00', 'Rescheduled due to emergency'),

-- December 2024 appointments (completed and recent)
(4, 3, 2, '2024-12-02 09:30:00', 'COMPLETED', 'Preventive care', 45, '2024-11-29 14:00:00', '2024-12-02 10:15:00', 'Preventive screening completed'),
(4, 3, 2, '2024-12-05 11:15:00', 'COMPLETED', 'Headache assessment', 30, '2024-12-02 16:00:00', '2024-12-05 11:45:00', 'Tension headaches, stress management advised'),
(4, 3, 2, '2024-12-09 15:00:00', 'COMPLETED', 'Annual wellness visit', 60, '2024-12-06 10:00:00', '2024-12-09 16:00:00', 'Comprehensive wellness check completed'),
(4, 3, 2, '2024-12-12 10:00:00', 'COMPLETED', 'Symptom evaluation', 30, '2024-12-09 12:00:00', '2024-12-12 10:30:00', 'Minor symptoms resolved'),

-- Current week appointments (various statuses)
(4, 3, 2, DATE_SUB(NOW(), INTERVAL 2 DAY), 'COMPLETED', 'Treatment planning', 45, DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY), 'Treatment plan updated'),
(4, 3, 2, DATE_SUB(NOW(), INTERVAL 1 DAY), 'COMPLETED', 'Health screening', 30, DATE_SUB(NOW(), INTERVAL 4 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), 'Screening results normal'),

-- Today's appointments
(4, 3, 2, CONCAT(CURDATE(), ' 09:00:00'), 'CONFIRMED', 'Morning consultation', 30, DATE_SUB(NOW(), INTERVAL 3 DAY), NOW(), 'Routine morning checkup'),
(4, 3, 2, CONCAT(CURDATE(), ' 14:00:00'), 'IN_PROGRESS', 'Afternoon checkup', 45, DATE_SUB(NOW(), INTERVAL 2 DAY), NOW(), 'Currently in progress'),
(4, 3, 2, CONCAT(CURDATE(), ' 16:30:00'), 'SCHEDULED', 'Evening consultation', 30, DATE_SUB(NOW(), INTERVAL 1 DAY), NOW(), 'Scheduled for later today'),

-- Future appointments (next 30 days)
(4, 3, 2, DATE_ADD(NOW(), INTERVAL 1 DAY), 'SCHEDULED', 'Follow-up appointment', 30, NOW(), NOW(), 'Follow-up from today visit'),
(4, 3, 2, DATE_ADD(NOW(), INTERVAL 3 DAY), 'CONFIRMED', 'Chronic condition management', 45, NOW(), NOW(), 'Regular chronic care visit'),
(4, 3, 2, DATE_ADD(NOW(), INTERVAL 5 DAY), 'SCHEDULED', 'Medication adjustment', 30, NOW(), NOW(), 'Review and adjust medications'),
(4, 3, 2, DATE_ADD(NOW(), INTERVAL 7 DAY), 'CONFIRMED', 'Post-surgery follow-up', 30, NOW(), NOW(), 'Post-operative check'),
(4, 3, 2, DATE_ADD(NOW(), INTERVAL 10 DAY), 'SCHEDULED', 'Emergency consultation', 45, NOW(), NOW(), 'Urgent care appointment'),
(4, 3, 2, DATE_ADD(NOW(), INTERVAL 14 DAY), 'SCHEDULED', 'Routine checkup', 30, NOW(), NOW(), 'Bi-weekly routine visit'),
(4, 3, 2, DATE_ADD(NOW(), INTERVAL 17 DAY), 'CONFIRMED', 'Lab work review', 30, NOW(), NOW(), 'Review upcoming lab results'),
(4, 3, 2, DATE_ADD(NOW(), INTERVAL 21 DAY), 'SCHEDULED', 'Specialist referral', 45, NOW(), NOW(), 'Referral consultation'),
(4, 3, 2, DATE_ADD(NOW(), INTERVAL 25 DAY), 'SCHEDULED', 'Quarterly review', 60, NOW(), NOW(), 'Quarterly health assessment'),
(4, 3, 2, DATE_ADD(NOW(), INTERVAL 28 DAY), 'CONFIRMED', 'Preventive screening', 45, NOW(), NOW(), 'Preventive health screening'),

-- Additional appointments for better statistics (using different patient/doctor combinations if available)
(4, 3, 2, '2024-10-15 10:00:00', 'COMPLETED', 'Initial consultation', 60, '2024-10-12 09:00:00', '2024-10-15 11:00:00', 'First visit completed'),
(4, 3, 2, '2024-10-22 14:30:00', 'COMPLETED', 'Treatment follow-up', 30, '2024-10-19 11:00:00', '2024-10-22 15:00:00', 'Treatment progressing well'),
(4, 3, 2, '2024-10-29 11:15:00', 'COMPLETED', 'Medication review', 30, '2024-10-26 16:00:00', '2024-10-29 11:45:00', 'Medication effectiveness reviewed'),

-- Some cancelled appointments for realistic data
(4, 3, 2, DATE_ADD(NOW(), INTERVAL 12 DAY), 'CANCELLED', 'Routine visit', 30, DATE_SUB(NOW(), INTERVAL 1 DAY), NOW(), 'Patient requested cancellation'),
(4, 3, 2, '2024-11-20 15:30:00', 'CANCELLED', 'Check-up appointment', 30, '2024-11-17 10:00:00', '2024-11-20 12:00:00', 'Doctor unavailable');

-- Update appointment counts and add some additional metadata
UPDATE appointments SET
    cancelled_at = DATE_SUB(appointment_date, INTERVAL 2 HOUR),
    cancellation_reason = 'Patient illness'
WHERE status = 'CANCELLED' AND cancelled_at IS NULL;

-- Add notes to appointments that don't have them
UPDATE appointments SET
    notes = CASE
        WHEN status = 'COMPLETED' THEN 'Appointment completed successfully'
        WHEN status = 'CANCELLED' THEN 'Appointment was cancelled'
        WHEN status = 'SCHEDULED' THEN 'Appointment scheduled'
        WHEN status = 'CONFIRMED' THEN 'Appointment confirmed by patient'
        WHEN status = 'IN_PROGRESS' THEN 'Appointment currently in progress'
        ELSE 'No additional notes'
    END
WHERE notes IS NULL OR notes = '';

-- Verify the data
SELECT
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM appointments), 2) as percentage
FROM appointments
GROUP BY status
ORDER BY count DESC;

SELECT
    DATE_FORMAT(appointment_date, '%Y-%m') as month,
    COUNT(*) as appointments_count
FROM appointments
GROUP BY DATE_FORMAT(appointment_date, '%Y-%m')
ORDER BY month DESC
LIMIT 6;

SELECT COUNT(*) as total_appointments FROM appointments;
