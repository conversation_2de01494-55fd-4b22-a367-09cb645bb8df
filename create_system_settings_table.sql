-- Create system_settings table for storing application configuration
CREATE TABLE IF NOT EXISTS system_settings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(255) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type VARCHAR(50) DEFAULT 'STRING',
    description TEXT,
    category VARCHAR(100),
    is_encrypted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by VARCHAR(255),
    version INT DEFAULT 1,
    
    INDEX idx_setting_key (setting_key),
    INDEX idx_category (category),
    INDEX idx_updated_at (updated_at)
);

-- Insert default security settings
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, category, description, updated_by) VALUES
-- Authentication Settings
('security.two_factor_auth_enabled', 'false', 'BOOLEAN', 'SECURITY', 'Enable two-factor authentication for all admin accounts', 'SYSTEM'),
('security.strong_password_policy_enabled', 'true', 'BOOLEAN', 'SECURITY', 'Enforce complex password requirements', 'SYSTEM'),
('security.session_timeout_minutes', '30', 'INTEGER', 'SECURITY', 'Session timeout in minutes', 'SYSTEM'),
('security.max_login_attempts', '5', 'INTEGER', 'SECURITY', 'Maximum login attempts before account lockout', 'SYSTEM'),

-- Data Security Settings
('security.data_encryption_enabled', 'true', 'BOOLEAN', 'SECURITY', 'Encrypt sensitive data at rest', 'SYSTEM'),
('security.audit_logging_enabled', 'true', 'BOOLEAN', 'SECURITY', 'Log all admin and system actions', 'SYSTEM'),

-- Additional Security Settings
('security.password_expiration_enabled', 'false', 'BOOLEAN', 'SECURITY', 'Enable password expiration policy', 'SYSTEM'),
('security.password_expiration_days', '90', 'INTEGER', 'SECURITY', 'Password expiration period in days', 'SYSTEM'),
('security.account_lockout_enabled', 'true', 'BOOLEAN', 'SECURITY', 'Enable account lockout after failed attempts', 'SYSTEM'),
('security.lockout_duration_minutes', '15', 'INTEGER', 'SECURITY', 'Account lockout duration in minutes', 'SYSTEM'),
('security.ip_whitelist_enabled', 'false', 'BOOLEAN', 'SECURITY', 'Enable IP address whitelisting', 'SYSTEM'),
('security.allowed_ip_addresses', '', 'STRING', 'SECURITY', 'Comma-separated list of allowed IP addresses', 'SYSTEM'),

-- Notification Settings
('notifications.email_enabled', 'true', 'BOOLEAN', 'NOTIFICATIONS', 'Enable email notifications', 'SYSTEM'),
('notifications.security_alerts_enabled', 'true', 'BOOLEAN', 'NOTIFICATIONS', 'Enable security alert notifications', 'SYSTEM'),
('notifications.login_notifications_enabled', 'false', 'BOOLEAN', 'NOTIFICATIONS', 'Enable login notification emails', 'SYSTEM'),

-- System Settings
('system.maintenance_mode_enabled', 'false', 'BOOLEAN', 'SYSTEM', 'Enable maintenance mode', 'SYSTEM'),
('system.maintenance_message', 'System is under maintenance. Please try again later.', 'STRING', 'SYSTEM', 'Maintenance mode message', 'SYSTEM'),

-- Application Settings
('app.version', '1.0.0', 'STRING', 'APPLICATION', 'Application version', 'SYSTEM'),
('app.environment', 'production', 'STRING', 'APPLICATION', 'Application environment', 'SYSTEM'),
('app.debug_mode_enabled', 'false', 'BOOLEAN', 'APPLICATION', 'Enable debug mode', 'SYSTEM'),

-- Backup Settings
('backup.auto_backup_enabled', 'true', 'BOOLEAN', 'BACKUP', 'Enable automatic backups', 'SYSTEM'),
('backup.backup_frequency_hours', '24', 'INTEGER', 'BACKUP', 'Backup frequency in hours', 'SYSTEM'),
('backup.retention_days', '30', 'INTEGER', 'BACKUP', 'Backup retention period in days', 'SYSTEM'),
('backup.backup_location', '/backups/mediconnect/', 'STRING', 'BACKUP', 'Backup storage location', 'SYSTEM'),

-- Email Configuration
('email.smtp_host', 'smtp.gmail.com', 'STRING', 'EMAIL', 'SMTP server host', 'SYSTEM'),
('email.smtp_port', '587', 'INTEGER', 'EMAIL', 'SMTP server port', 'SYSTEM'),
('email.smtp_username', '', 'STRING', 'EMAIL', 'SMTP username', 'SYSTEM'),
('email.smtp_password', '', 'STRING', 'EMAIL', 'SMTP password (encrypted)', 'SYSTEM'),
('email.from_address', '<EMAIL>', 'STRING', 'EMAIL', 'Default from email address', 'SYSTEM'),
('email.from_name', 'MediConnect System', 'STRING', 'EMAIL', 'Default from name', 'SYSTEM'),

-- Audit Settings
('audit.log_retention_days', '90', 'INTEGER', 'AUDIT', 'Audit log retention period in days', 'SYSTEM'),
('audit.log_level', 'INFO', 'STRING', 'AUDIT', 'Audit logging level', 'SYSTEM'),
('audit.log_failed_logins', 'true', 'BOOLEAN', 'AUDIT', 'Log failed login attempts', 'SYSTEM'),
('audit.log_admin_actions', 'true', 'BOOLEAN', 'AUDIT', 'Log all admin actions', 'SYSTEM'),
('audit.log_data_changes', 'true', 'BOOLEAN', 'AUDIT', 'Log data modification events', 'SYSTEM');

-- Show the created settings
SELECT 'System settings table created and populated successfully!' as status;
SELECT category, COUNT(*) as setting_count FROM system_settings GROUP BY category ORDER BY category;
SELECT COUNT(*) as total_settings FROM system_settings;
