// 📊 Fixed Admin Reports Page - Matching Backend API Format
import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { reportsService, UserGrowthResponse, SystemStatsResponse } from './reportsService-fixed';

export const AdminReportsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'appointments' | 'performance'>('overview');

  // 📊 Fetch system statistics
  const { data: systemStats, isLoading: statsLoading, error: statsError } = useQuery({
    queryKey: ['reports', 'system-stats'],
    queryFn: () => reportsService.getSystemStats(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
  });

  // 📈 Fetch user growth data (fixed)
  const { data: userGrowth, isLoading: growthLoading, error: growthError } = useQuery({
    queryKey: ['reports', 'user-growth'],
    queryFn: () => reportsService.getUserGrowthData(),
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 3,
  });

  // 🚀 Fetch performance metrics
  const { data: performanceMetrics, isLoading: performanceLoading } = useQuery({
    queryKey: ['reports', 'performance-metrics'],
    queryFn: () => reportsService.getPerformanceMetrics(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 3,
  });

  // 📅 Fetch appointment analytics
  const { data: appointmentAnalytics, isLoading: appointmentLoading } = useQuery({
    queryKey: ['reports', 'appointment-analytics'],
    queryFn: () => reportsService.getAppointmentAnalytics(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
  });

  // 📄 Export PDF handlers
  const handleExportSystemOverview = async () => {
    try {
      const blob = await reportsService.exportSystemOverviewPDF();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `system-overview-${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      alert('Failed to export PDF. Please try again.');
    }
  };

  const handleExportUsersReport = async () => {
    try {
      const blob = await reportsService.exportUsersReportPDF();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `users-report-${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      alert('Failed to export users PDF. Please try again.');
    }
  };

  return (
    <div className="admin-reports-page">
      {/* 📊 Header */}
      <div className="mb-8">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">📊 System Reports</h1>
          <div className="flex space-x-3">
            <button
              onClick={handleExportSystemOverview}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              📄 Export Overview PDF
            </button>
            <button
              onClick={handleExportUsersReport}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              👥 Export Users PDF
            </button>
          </div>
        </div>
      </div>

      {/* 📋 Navigation Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'overview', label: 'Overview', icon: '📊' },
              { key: 'users', label: 'Users', icon: '👥' },
              { key: 'appointments', label: 'Appointments', icon: '📅' },
              { key: 'performance', label: 'Performance', icon: '🚀' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon} {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* 📊 Content */}
      <div className="reports-content">
        {activeTab === 'overview' && (
          <OverviewTab 
            systemStats={systemStats} 
            userGrowth={userGrowth}
            isLoading={statsLoading || growthLoading}
            error={statsError || growthError}
          />
        )}
        
        {activeTab === 'users' && (
          <UsersTab 
            userGrowth={userGrowth}
            systemStats={systemStats}
            isLoading={growthLoading || statsLoading}
            error={growthError || statsError}
          />
        )}
        
        {activeTab === 'appointments' && (
          <AppointmentsTab 
            appointmentAnalytics={appointmentAnalytics}
            systemStats={systemStats}
            isLoading={appointmentLoading || statsLoading}
          />
        )}
        
        {activeTab === 'performance' && (
          <PerformanceTab 
            performanceMetrics={performanceMetrics}
            isLoading={performanceLoading}
          />
        )}
      </div>
    </div>
  );
};

// 📊 Overview Tab Component
const OverviewTab: React.FC<{
  systemStats?: SystemStatsResponse;
  userGrowth?: UserGrowthResponse;
  isLoading: boolean;
  error: any;
}> = ({ systemStats, userGrowth, isLoading, error }) => {
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-600">Loading system overview...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <h3 className="text-red-800 font-semibold mb-2">❌ Error Loading Data</h3>
        <p className="text-red-600 mb-4">Failed to load system overview data.</p>
        <details className="text-sm text-red-500">
          <summary className="cursor-pointer">Error Details</summary>
          <pre className="mt-2 whitespace-pre-wrap">{error?.message || 'Unknown error'}</pre>
        </details>
      </div>
    );
  }

  return (
    <div className="overview-tab">
      {/* 📊 Key Metrics */}
      {systemStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <MetricCard
            title="Total Users"
            value={systemStats.totalUsers}
            icon="👥"
            color="blue"
            subtitle={`${systemStats.activeUsers} active`}
          />
          <MetricCard
            title="Total Appointments"
            value={systemStats.totalAppointments}
            icon="📅"
            color="green"
            subtitle={`${systemStats.completedAppointments} completed`}
          />
          <MetricCard
            title="System Uptime"
            value={systemStats.systemUptime}
            icon="🚀"
            color="purple"
            subtitle={`API: ${systemStats.apiResponseTime}`}
          />
          <MetricCard
            title="Database Status"
            value={systemStats.databaseStatus}
            icon="🗄️"
            color="orange"
            subtitle={`Last backup: ${systemStats.lastBackup}`}
          />
        </div>
      )}

      {/* 📈 Growth Metrics */}
      {systemStats && (
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">📈 Growth Metrics</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{systemStats.newUsersThisMonth}</div>
              <div className="text-sm text-gray-600">New Users This Month</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{systemStats.newUsersLastMonth}</div>
              <div className="text-sm text-gray-600">New Users Last Month</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${systemStats.userGrowthRate >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {systemStats.userGrowthRate >= 0 ? '+' : ''}{systemStats.userGrowthRate}%
              </div>
              <div className="text-sm text-gray-600">Growth Rate</div>
            </div>
          </div>
        </div>
      )}

      {/* 👥 Role Distribution */}
      {userGrowth && (
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">👥 User Role Distribution</h3>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {Object.entries(userGrowth.roleDistribution).map(([role, count]) => (
              <div key={role} className="text-center">
                <div className="text-xl font-bold text-gray-900">{count}</div>
                <div className="text-sm text-gray-600">{role.replace('_', ' ')}</div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

// 📊 Metric Card Component
const MetricCard: React.FC<{
  title: string;
  value: string | number;
  icon: string;
  color: 'blue' | 'green' | 'purple' | 'orange';
  subtitle?: string;
}> = ({ title, value, icon, color, subtitle }) => {
  const colorClasses = {
    blue: 'bg-blue-50 border-blue-200 text-blue-800',
    green: 'bg-green-50 border-green-200 text-green-800',
    purple: 'bg-purple-50 border-purple-200 text-purple-800',
    orange: 'bg-orange-50 border-orange-200 text-orange-800',
  };

  return (
    <div className={`p-6 rounded-lg border ${colorClasses[color]}`}>
      <div className="flex items-center justify-between mb-2">
        <span className="text-2xl">{icon}</span>
        <div className="text-right">
          <div className="text-2xl font-bold">{value}</div>
          <div className="text-sm font-medium opacity-75">{title}</div>
        </div>
      </div>
      {subtitle && (
        <div className="text-xs opacity-60 mt-2">{subtitle}</div>
      )}
    </div>
  );
};
