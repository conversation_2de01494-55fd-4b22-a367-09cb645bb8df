// 📅 Doctor Appointments Page with Real Database Integration
import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { doctorAppointmentsService, RescheduleRequest, NotesRequest } from './doctorAppointmentsDatabase';
import { AppointmentDTO } from './doctorPatientsDatabase';

interface DoctorAppointmentsPageProps {
  doctorId: number;
}

type AppointmentTab = 'today' | 'upcoming' | 'completed';

export const DoctorAppointmentsPage: React.FC<DoctorAppointmentsPageProps> = ({ doctorId }) => {
  const [activeTab, setActiveTab] = useState<AppointmentTab>('today');
  const [selectedAppointment, setSelectedAppointment] = useState<AppointmentDTO | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showRescheduleModal, setShowRescheduleModal] = useState(false);
  const [showNotesModal, setShowNotesModal] = useState(false);
  const queryClient = useQueryClient();

  // 📊 Fetch appointment statistics from database
  const { data: statistics, isLoading: statsLoading } = useQuery({
    queryKey: ['doctor', doctorId, 'appointments', 'statistics'],
    queryFn: () => doctorAppointmentsService.getAppointmentStatistics(doctorId),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // 📅 Fetch today's appointments from database
  const { data: todayAppointments, isLoading: todayLoading } = useQuery({
    queryKey: ['doctor', doctorId, 'appointments', 'today'],
    queryFn: () => doctorAppointmentsService.getTodayAppointments(doctorId),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // ⏰ Fetch upcoming appointments from database
  const { data: upcomingAppointments, isLoading: upcomingLoading } = useQuery({
    queryKey: ['doctor', doctorId, 'appointments', 'upcoming'],
    queryFn: () => doctorAppointmentsService.getUpcomingAppointments(doctorId),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // ✅ Fetch completed appointments from database
  const { data: completedAppointments, isLoading: completedLoading } = useQuery({
    queryKey: ['doctor', doctorId, 'appointments', 'completed'],
    queryFn: () => doctorAppointmentsService.getCompletedAppointments(doctorId),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // 🔄 Reschedule appointment mutation
  const rescheduleMutation = useMutation({
    mutationFn: ({ appointmentId, rescheduleData }: { appointmentId: number; rescheduleData: RescheduleRequest }) =>
      doctorAppointmentsService.rescheduleAppointment(doctorId, appointmentId, rescheduleData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['doctor', doctorId, 'appointments'] });
      setShowRescheduleModal(false);
      alert('✅ Appointment rescheduled successfully!');
    },
    onError: (error) => {
      console.error('❌ Failed to reschedule appointment:', error);
      alert('❌ Failed to reschedule appointment. Please try again.');
    },
  });

  // 📝 Update notes mutation
  const updateNotesMutation = useMutation({
    mutationFn: ({ appointmentId, notesData }: { appointmentId: number; notesData: NotesRequest }) =>
      doctorAppointmentsService.updateAppointmentNotes(doctorId, appointmentId, notesData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['doctor', doctorId, 'appointments'] });
      setShowNotesModal(false);
      alert('✅ Appointment notes updated successfully!');
    },
    onError: (error) => {
      console.error('❌ Failed to update notes:', error);
      alert('❌ Failed to update notes. Please try again.');
    },
  });

  // ❌ Cancel appointment mutation
  const cancelMutation = useMutation({
    mutationFn: ({ appointmentId, reason }: { appointmentId: number; reason: string }) =>
      doctorAppointmentsService.cancelAppointment(appointmentId, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['doctor', doctorId, 'appointments'] });
      alert('✅ Appointment cancelled successfully!');
    },
    onError: (error) => {
      console.error('❌ Failed to cancel appointment:', error);
      alert('❌ Failed to cancel appointment. Please try again.');
    },
  });

  const getCurrentAppointments = () => {
    switch (activeTab) {
      case 'today':
        return { data: todayAppointments, isLoading: todayLoading };
      case 'upcoming':
        return { data: upcomingAppointments, isLoading: upcomingLoading };
      case 'completed':
        return { data: completedAppointments, isLoading: completedLoading };
      default:
        return { data: [], isLoading: false };
    }
  };

  const { data: currentAppointments, isLoading: currentLoading } = getCurrentAppointments();

  return (
    <div className="doctor-appointments-page">
      {/* 📊 Statistics Dashboard */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">📅 My Appointments</h1>
        
        {statistics && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
            <StatCard title="Total" value={statistics.total} icon="📊" color="blue" />
            <StatCard title="Today" value={statistics.today} icon="📅" color="green" />
            <StatCard title="Upcoming" value={statistics.upcoming} icon="⏰" color="purple" />
            <StatCard title="Completed" value={statistics.completed} icon="✅" color="orange" />
            <StatCard title="Cancelled" value={statistics.cancelled} icon="❌" color="red" />
          </div>
        )}
      </div>

      {/* 📋 Appointment Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { key: 'today', label: 'Today', count: statistics?.today || 0, icon: '📅' },
              { key: 'upcoming', label: 'Upcoming', count: statistics?.upcoming || 0, icon: '⏰' },
              { key: 'completed', label: 'Completed', count: statistics?.completed || 0, icon: '✅' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as AppointmentTab)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon} {tab.label} ({tab.count})
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* 📋 Appointments List */}
      <div className="appointments-content">
        {currentLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <span className="ml-3 text-gray-600">Loading appointments from database...</span>
          </div>
        ) : currentAppointments && currentAppointments.length > 0 ? (
          <div className="space-y-4">
            {currentAppointments.map((appointment) => (
              <AppointmentCard
                key={appointment.id}
                appointment={appointment}
                onViewDetails={() => {
                  setSelectedAppointment(appointment);
                  setShowDetailsModal(true);
                }}
                onReschedule={() => {
                  setSelectedAppointment(appointment);
                  setShowRescheduleModal(true);
                }}
                onAddNotes={() => {
                  setSelectedAppointment(appointment);
                  setShowNotesModal(true);
                }}
                onCancel={(reason) => {
                  cancelMutation.mutate({ appointmentId: appointment.id, reason });
                }}
                activeTab={activeTab}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📅</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No appointments found</h3>
            <p className="text-gray-500">
              {activeTab === 'today' && "You don't have any appointments scheduled for today."}
              {activeTab === 'upcoming' && "You don't have any upcoming appointments."}
              {activeTab === 'completed' && "You don't have any completed appointments."}
            </p>
          </div>
        )}
      </div>

      {/* 🔍 Appointment Details Modal */}
      {showDetailsModal && selectedAppointment && (
        <AppointmentDetailsModal
          appointment={selectedAppointment}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedAppointment(null);
          }}
        />
      )}

      {/* 🔄 Reschedule Modal */}
      {showRescheduleModal && selectedAppointment && (
        <RescheduleAppointmentModal
          appointment={selectedAppointment}
          onClose={() => {
            setShowRescheduleModal(false);
            setSelectedAppointment(null);
          }}
          onReschedule={(rescheduleData) => {
            rescheduleMutation.mutate({
              appointmentId: selectedAppointment.id,
              rescheduleData,
            });
          }}
          isLoading={rescheduleMutation.isPending}
        />
      )}

      {/* 📝 Notes Modal */}
      {showNotesModal && selectedAppointment && (
        <AppointmentNotesModal
          appointment={selectedAppointment}
          onClose={() => {
            setShowNotesModal(false);
            setSelectedAppointment(null);
          }}
          onUpdateNotes={(notesData) => {
            updateNotesMutation.mutate({
              appointmentId: selectedAppointment.id,
              notesData,
            });
          }}
          isLoading={updateNotesMutation.isPending}
        />
      )}
    </div>
  );
};

// 📊 Statistics Card Component
const StatCard: React.FC<{
  title: string;
  value: string | number;
  icon: string;
  color: 'blue' | 'green' | 'purple' | 'orange' | 'red';
}> = ({ title, value, icon, color }) => {
  const colorClasses = {
    blue: 'bg-blue-50 border-blue-200 text-blue-800',
    green: 'bg-green-50 border-green-200 text-green-800',
    purple: 'bg-purple-50 border-purple-200 text-purple-800',
    orange: 'bg-orange-50 border-orange-200 text-orange-800',
    red: 'bg-red-50 border-red-200 text-red-800',
  };

  return (
    <div className={`p-4 rounded-lg border ${colorClasses[color]}`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium opacity-75">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
        </div>
        <span className="text-2xl">{icon}</span>
      </div>
    </div>
  );
};

// 📅 Appointment Card Component
const AppointmentCard: React.FC<{
  appointment: AppointmentDTO;
  onViewDetails: () => void;
  onReschedule: () => void;
  onAddNotes: () => void;
  onCancel: (reason: string) => void;
  activeTab: AppointmentTab;
}> = ({ appointment, onViewDetails, onReschedule, onAddNotes, onCancel, activeTab }) => {
  const formatTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDate = (dateTime: string) => {
    return new Date(dateTime).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const getStatusBadge = (status: AppointmentDTO['status']) => {
    const statusConfig = {
      SCHEDULED: { color: 'bg-blue-100 text-blue-800', icon: '📅' },
      CONFIRMED: { color: 'bg-green-100 text-green-800', icon: '✅' },
      IN_PROGRESS: { color: 'bg-yellow-100 text-yellow-800', icon: '🔄' },
      COMPLETED: { color: 'bg-gray-100 text-gray-800', icon: '✅' },
      CANCELLED: { color: 'bg-red-100 text-red-800', icon: '❌' },
      NO_SHOW: { color: 'bg-orange-100 text-orange-800', icon: '👻' },
    };

    const config = statusConfig[status];
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.icon} {status.replace('_', ' ')}
      </span>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
      <div className="flex items-start justify-between">
        {/* Appointment Info */}
        <div className="flex-1">
          <div className="flex items-center space-x-4 mb-3">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{formatTime(appointment.appointmentDate)}</div>
              <div className="text-sm text-gray-500">{appointment.durationMinutes}min</div>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900">{appointment.patientName}</h3>
              <p className="text-sm text-gray-600">📧 {appointment.patientEmail}</p>
              <p className="text-sm text-gray-600">📅 {formatDate(appointment.appointmentDate)}</p>
            </div>
          </div>

          <div className="mb-3">
            <p className="text-sm font-medium text-gray-700">Reason:</p>
            <p className="text-sm text-gray-600">{appointment.reason}</p>
          </div>

          {appointment.notes && (
            <div className="mb-3">
              <p className="text-sm font-medium text-gray-700">Notes:</p>
              <p className="text-sm text-gray-600">{appointment.notes}</p>
            </div>
          )}
        </div>

        {/* Status and Actions */}
        <div className="ml-4 flex flex-col items-end space-y-3">
          {getStatusBadge(appointment.status)}

          <div className="flex flex-col space-y-2">
            <button
              onClick={onViewDetails}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              👁️ View Details
            </button>

            {activeTab !== 'completed' && appointment.status !== 'CANCELLED' && (
              <>
                <button
                  onClick={onReschedule}
                  className="text-purple-600 hover:text-purple-800 text-sm font-medium"
                >
                  🔄 Reschedule
                </button>
                <button
                  onClick={onAddNotes}
                  className="text-green-600 hover:text-green-800 text-sm font-medium"
                >
                  📝 Add Notes
                </button>
                <button
                  onClick={() => {
                    const reason = prompt('Please provide a reason for cancellation:');
                    if (reason) onCancel(reason);
                  }}
                  className="text-red-600 hover:text-red-800 text-sm font-medium"
                >
                  ❌ Cancel
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
