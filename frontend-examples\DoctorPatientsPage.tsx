// 🏥 Doctor Patients Page with Real Database Integration
import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { doctorPatientsService, PatientDTO, AppointmentDTO, PatientStatistics } from './doctorPatientsDatabase';

interface DoctorPatientsPageProps {
  doctorId: number;
}

export const DoctorPatientsPage: React.FC<DoctorPatientsPageProps> = ({ doctorId }) => {
  const [selectedPatient, setSelectedPatient] = useState<PatientDTO | null>(null);
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const queryClient = useQueryClient();

  // 📊 Fetch patients from database
  const { data: patients, isLoading: patientsLoading, error: patientsError } = useQuery({
    queryKey: ['doctor', doctorId, 'patients'],
    queryFn: () => doctorPatientsService.getPatients(doctorId),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // 📈 Fetch patient statistics from database
  const { data: statistics, isLoading: statsLoading } = useQuery({
    queryKey: ['doctor', doctorId, 'patients', 'statistics'],
    queryFn: () => doctorPatientsService.getPatientStatistics(doctorId),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // 📝 Schedule appointment mutation
  const scheduleAppointmentMutation = useMutation({
    mutationFn: ({ patientId, appointmentData }: { patientId: number; appointmentData: any }) =>
      doctorPatientsService.scheduleAppointment(doctorId, patientId, appointmentData),
    onSuccess: () => {
      // Invalidate and refetch patients data
      queryClient.invalidateQueries({ queryKey: ['doctor', doctorId, 'patients'] });
      queryClient.invalidateQueries({ queryKey: ['doctor', doctorId, 'patients', 'statistics'] });
      setShowScheduleModal(false);
      alert('✅ Appointment scheduled successfully!');
    },
    onError: (error) => {
      console.error('❌ Failed to schedule appointment:', error);
      alert('❌ Failed to schedule appointment. Please try again.');
    },
  });

  if (patientsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-gray-600">Loading patients from database...</span>
      </div>
    );
  }

  if (patientsError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <h3 className="text-red-800 font-semibold">❌ Database Error</h3>
        <p className="text-red-600">Failed to load patients from database. Please try again.</p>
      </div>
    );
  }

  return (
    <div className="doctor-patients-page">
      {/* 📊 Statistics Dashboard */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">👥 My Patients</h1>
        
        {statistics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <StatCard
              title="Total Patients"
              value={statistics.totalPatients}
              icon="👥"
              color="blue"
            />
            <StatCard
              title="Average Age"
              value={`${statistics.averageAge} years`}
              icon="📊"
              color="green"
            />
            <StatCard
              title="Total Appointments"
              value={statistics.appointmentStats.totalAppointments}
              icon="📅"
              color="purple"
            />
            <StatCard
              title="Today's Appointments"
              value={statistics.appointmentStats.todayAppointments}
              icon="🕐"
              color="orange"
            />
          </div>
        )}
      </div>

      {/* 👥 Patients List */}
      <div className="patients-grid">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {patients?.map((patient) => (
            <PatientCard
              key={patient.id}
              patient={patient}
              onScheduleAppointment={() => {
                setSelectedPatient(patient);
                setShowScheduleModal(true);
              }}
              onViewAppointments={() => {
                // Navigate to patient appointments view
                window.location.href = `/doctor/patients/${patient.id}/appointments`;
              }}
              onViewMedicalHistory={() => {
                // Navigate to medical history
                window.location.href = `/doctor/patients/${patient.id}/history`;
              }}
            />
          ))}
        </div>
      </div>

      {/* 📅 Schedule Appointment Modal */}
      {showScheduleModal && selectedPatient && (
        <ScheduleAppointmentModal
          doctorId={doctorId}
          patient={selectedPatient}
          onClose={() => {
            setShowScheduleModal(false);
            setSelectedPatient(null);
          }}
          onSchedule={(appointmentData) => {
            scheduleAppointmentMutation.mutate({
              patientId: selectedPatient.id,
              appointmentData,
            });
          }}
          isLoading={scheduleAppointmentMutation.isPending}
        />
      )}
    </div>
  );
};

// 📊 Statistics Card Component
const StatCard: React.FC<{
  title: string;
  value: string | number;
  icon: string;
  color: 'blue' | 'green' | 'purple' | 'orange';
}> = ({ title, value, icon, color }) => {
  const colorClasses = {
    blue: 'bg-blue-50 border-blue-200 text-blue-800',
    green: 'bg-green-50 border-green-200 text-green-800',
    purple: 'bg-purple-50 border-purple-200 text-purple-800',
    orange: 'bg-orange-50 border-orange-200 text-orange-800',
  };

  return (
    <div className={`p-4 rounded-lg border ${colorClasses[color]}`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium opacity-75">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
        </div>
        <span className="text-2xl">{icon}</span>
      </div>
    </div>
  );
};

// 👤 Patient Card Component
const PatientCard: React.FC<{
  patient: PatientDTO;
  onScheduleAppointment: () => void;
  onViewAppointments: () => void;
  onViewMedicalHistory: () => void;
}> = ({ patient, onScheduleAppointment, onViewAppointments, onViewMedicalHistory }) => {
  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
      {/* Patient Info */}
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-900">{patient.name}</h3>
        <p className="text-sm text-gray-600">ID: {patient.patientId}</p>
        <p className="text-sm text-gray-600">📧 {patient.email}</p>
        <p className="text-sm text-gray-600">🩸 {patient.bloodGroup}</p>
      </div>

      {/* Appointment Summary */}
      <div className="mb-4 p-3 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-700 mb-2">📅 Appointment Summary</h4>
        <div className="space-y-1 text-xs text-gray-600">
          <p>Total: {patient.totalAppointments || 0} appointments</p>
          {patient.lastAppointment && (
            <p>Last: {new Date(patient.lastAppointment.appointmentDate).toLocaleDateString()}</p>
          )}
          {patient.nextAppointment && (
            <p>Next: {new Date(patient.nextAppointment.appointmentDate).toLocaleDateString()}</p>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="space-y-2">
        <button
          onClick={onScheduleAppointment}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
        >
          📅 Schedule Appointment
        </button>
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={onViewAppointments}
            className="bg-gray-100 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-200 transition-colors text-sm"
          >
            📋 Appointments
          </button>
          <button
            onClick={onViewMedicalHistory}
            className="bg-gray-100 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-200 transition-colors text-sm"
          >
            🏥 History
          </button>
        </div>
      </div>
    </div>
  );
};
