// 📅 Doctor Appointments Database Service
// Real database integration for /doctor/appointments route

import { api } from '../api/api';
import { AppointmentDTO } from './doctorPatientsDatabase';

export interface RescheduleRequest {
  newDateTime: string; // ISO datetime
  reason: string;
}

export interface NotesRequest {
  notes: string;
  diagnosis?: string;
}

export interface AppointmentFilters {
  status?: 'SCHEDULED' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'NO_SHOW';
  dateFrom?: string;
  dateTo?: string;
  patientName?: string;
}

// 🔧 Doctor Appointments Database Service
export class DoctorAppointmentsService {
  
  // 📅 Get today's appointments from database
  async getTodayAppointments(doctorId: number): Promise<AppointmentDTO[]> {
    console.log('🚀 Fetching today\'s appointments from database for doctor:', doctorId);
    
    try {
      const response = await api.get(`/doctor/${doctorId}/appointments/today`);
      const appointments = response.data.data || response.data;
      
      console.log('✅ Today\'s appointments fetched from database:', appointments);
      return appointments;
    } catch (error) {
      console.error('❌ Failed to fetch today\'s appointments from database:', error);
      throw error;
    }
  }

  // ⏰ Get upcoming appointments from database
  async getUpcomingAppointments(doctorId: number): Promise<AppointmentDTO[]> {
    console.log('🚀 Fetching upcoming appointments from database for doctor:', doctorId);
    
    try {
      const response = await api.get(`/doctor/${doctorId}/appointments/upcoming`);
      const appointments = response.data.data || response.data;
      
      console.log('✅ Upcoming appointments fetched from database:', appointments);
      return appointments;
    } catch (error) {
      console.error('❌ Failed to fetch upcoming appointments from database:', error);
      throw error;
    }
  }

  // ✅ Get completed appointments from database
  async getCompletedAppointments(doctorId: number): Promise<AppointmentDTO[]> {
    console.log('🚀 Fetching completed appointments from database for doctor:', doctorId);
    
    try {
      const response = await api.get(`/doctor/${doctorId}/appointments/completed`);
      const appointments = response.data.data || response.data;
      
      console.log('✅ Completed appointments fetched from database:', appointments);
      return appointments;
    } catch (error) {
      console.error('❌ Failed to fetch completed appointments from database:', error);
      throw error;
    }
  }

  // 🔍 Get appointment details from database
  async getAppointmentDetails(appointmentId: number): Promise<AppointmentDTO> {
    console.log('🚀 Fetching appointment details from database:', appointmentId);
    
    try {
      const response = await api.get(`/appointments/${appointmentId}`);
      const appointment = response.data;
      
      console.log('✅ Appointment details fetched from database:', appointment);
      return appointment;
    } catch (error) {
      console.error('❌ Failed to fetch appointment details from database:', error);
      throw error;
    }
  }

  // 🔄 Reschedule appointment in database
  async rescheduleAppointment(doctorId: number, appointmentId: number, rescheduleData: RescheduleRequest): Promise<AppointmentDTO> {
    console.log('🚀 Rescheduling appointment in database:', { doctorId, appointmentId, rescheduleData });
    
    try {
      // Using the DoctorService reschedule method
      const response = await api.put(`/doctor/${doctorId}/appointments/${appointmentId}/reschedule`, rescheduleData);
      const appointment = response.data.data || response.data;
      
      console.log('✅ Appointment rescheduled in database:', appointment);
      return appointment;
    } catch (error) {
      console.error('❌ Failed to reschedule appointment in database:', error);
      throw error;
    }
  }

  // 📝 Update appointment notes in database
  async updateAppointmentNotes(doctorId: number, appointmentId: number, notesData: NotesRequest): Promise<AppointmentDTO> {
    console.log('🚀 Updating appointment notes in database:', { doctorId, appointmentId, notesData });
    
    try {
      // Using the DoctorService notes update method
      const response = await api.put(`/doctor/${doctorId}/appointments/${appointmentId}/notes`, notesData);
      const appointment = response.data.data || response.data;
      
      console.log('✅ Appointment notes updated in database:', appointment);
      return appointment;
    } catch (error) {
      console.error('❌ Failed to update appointment notes in database:', error);
      throw error;
    }
  }

  // ❌ Cancel appointment in database
  async cancelAppointment(appointmentId: number, reason: string): Promise<void> {
    console.log('🚀 Cancelling appointment in database:', { appointmentId, reason });
    
    try {
      await api.put(`/appointments/${appointmentId}/cancel`, null, {
        params: { reason }
      });
      
      console.log('✅ Appointment cancelled in database');
    } catch (error) {
      console.error('❌ Failed to cancel appointment in database:', error);
      throw error;
    }
  }

  // 🔄 Update appointment status in database
  async updateAppointmentStatus(appointmentId: number, status: AppointmentDTO['status']): Promise<AppointmentDTO> {
    console.log('🚀 Updating appointment status in database:', { appointmentId, status });
    
    try {
      const response = await api.put(`/appointments/${appointmentId}/status`, null, {
        params: { status }
      });
      const appointment = response.data;
      
      console.log('✅ Appointment status updated in database:', appointment);
      return appointment;
    } catch (error) {
      console.error('❌ Failed to update appointment status in database:', error);
      throw error;
    }
  }

  // 📊 Get all doctor's appointments with filters from database
  async getDoctorAppointments(doctorId: number, filters?: AppointmentFilters): Promise<AppointmentDTO[]> {
    console.log('🚀 Fetching doctor appointments from database:', { doctorId, filters });
    
    try {
      const response = await api.get(`/appointments/doctor/${doctorId}`, {
        params: filters
      });
      const appointments = response.data;
      
      console.log('✅ Doctor appointments fetched from database:', appointments);
      return appointments;
    } catch (error) {
      console.error('❌ Failed to fetch doctor appointments from database:', error);
      throw error;
    }
  }

  // 📈 Get appointment statistics from database
  async getAppointmentStatistics(doctorId: number): Promise<{
    total: number;
    today: number;
    upcoming: number;
    completed: number;
    cancelled: number;
    statusDistribution: Record<string, number>;
  }> {
    console.log('🚀 Fetching appointment statistics from database for doctor:', doctorId);
    
    try {
      // Fetch all appointments and calculate statistics
      const [todayAppts, upcomingAppts, completedAppts, allAppts] = await Promise.all([
        this.getTodayAppointments(doctorId),
        this.getUpcomingAppointments(doctorId),
        this.getCompletedAppointments(doctorId),
        this.getDoctorAppointments(doctorId)
      ]);

      const statusDistribution = allAppts.reduce((acc, apt) => {
        acc[apt.status] = (acc[apt.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const statistics = {
        total: allAppts.length,
        today: todayAppts.length,
        upcoming: upcomingAppts.length,
        completed: completedAppts.length,
        cancelled: statusDistribution['CANCELLED'] || 0,
        statusDistribution
      };
      
      console.log('✅ Appointment statistics calculated from database:', statistics);
      return statistics;
    } catch (error) {
      console.error('❌ Failed to fetch appointment statistics from database:', error);
      throw error;
    }
  }

  // 🔍 Search appointments in database
  async searchAppointments(doctorId: number, searchTerm: string): Promise<AppointmentDTO[]> {
    console.log('🚀 Searching appointments in database:', { doctorId, searchTerm });
    
    try {
      const allAppointments = await this.getDoctorAppointments(doctorId);
      
      // Client-side filtering (could be moved to backend for better performance)
      const filteredAppointments = allAppointments.filter(apt => 
        apt.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        apt.reason.toLowerCase().includes(searchTerm.toLowerCase()) ||
        apt.notes?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      
      console.log('✅ Appointments search completed:', filteredAppointments);
      return filteredAppointments;
    } catch (error) {
      console.error('❌ Failed to search appointments in database:', error);
      throw error;
    }
  }
}

export const doctorAppointmentsService = new DoctorAppointmentsService();
