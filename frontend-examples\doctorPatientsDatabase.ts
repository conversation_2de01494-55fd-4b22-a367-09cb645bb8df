// 🏥 Doctor Patients Database Service
// Real database integration for /doctor/patients route

import { api } from '../api/api';

// 📋 Database DTOs (matching backend)
export interface AppointmentDTO {
  id: number;
  patientId: number;
  patientName: string;
  patientEmail: string;
  doctorId: number;
  doctorName: string;
  doctorSpecialty: string;
  clinicId?: number;
  clinicName?: string;
  appointmentDate: string; // ISO datetime
  status: 'SCHEDULED' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'NO_SHOW';
  reason: string;
  notes?: string;
  durationMinutes: number;
  createdAt: string;
  updatedAt: string;
  cancellationReason?: string;
}

export interface PatientDTO {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  patientId: string;
  bloodGroup: string;
  allergies: string;
  medicalHistory: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  // Extended with appointment data
  lastAppointment?: AppointmentDTO;
  nextAppointment?: AppointmentDTO;
  totalAppointments?: number;
}

export interface AppointmentRequest {
  doctorId: number;
  patientId: number;
  appointmentDate: string; // ISO datetime
  reason: string;
  notes?: string;
  durationMinutes?: number;
}

export interface PatientStatistics {
  totalPatients: number;
  averageAge: number;
  genderDistribution: {
    male: number;
    female: number;
    other: number;
  };
  bloodGroupDistribution: Record<string, number>;
  appointmentStats: {
    totalAppointments: number;
    todayAppointments: number;
    upcomingAppointments: number;
    completedAppointments: number;
  };
}

// 🔧 Doctor Patients Database Service
export class DoctorPatientsService {
  
  // 📊 Get patients with appointment data from database
  async getPatients(doctorId: number): Promise<PatientDTO[]> {
    console.log('🚀 Fetching patients from database for doctor:', doctorId);
    
    try {
      const response = await api.get(`/doctor/${doctorId}/patients`);
      const patients: PatientDTO[] = response.data.data;
      
      // Enhance patients with appointment data
      const enhancedPatients = await Promise.all(
        patients.map(async (patient) => {
          const appointmentData = await this.getPatientAppointmentSummary(patient.id);
          return {
            ...patient,
            ...appointmentData
          };
        })
      );
      
      console.log('✅ Patients with appointments fetched from database:', enhancedPatients);
      return enhancedPatients;
    } catch (error) {
      console.error('❌ Failed to fetch patients from database:', error);
      throw error;
    }
  }

  // 📈 Get patient statistics from database
  async getPatientStatistics(doctorId: number): Promise<PatientStatistics> {
    console.log('🚀 Fetching patient statistics from database for doctor:', doctorId);
    
    try {
      const response = await api.get(`/doctor/${doctorId}/patients/statistics`);
      const stats = response.data.data;
      
      console.log('✅ Patient statistics fetched from database:', stats);
      return stats;
    } catch (error) {
      console.error('❌ Failed to fetch patient statistics from database:', error);
      throw error;
    }
  }

  // 📅 Get patient appointment summary from database
  async getPatientAppointmentSummary(patientId: number): Promise<{
    lastAppointment?: AppointmentDTO;
    nextAppointment?: AppointmentDTO;
    totalAppointments: number;
  }> {
    try {
      const response = await api.get(`/appointments/patient/${patientId}`);
      const appointments: AppointmentDTO[] = response.data;
      
      const now = new Date();
      const pastAppointments = appointments
        .filter(apt => new Date(apt.appointmentDate) < now)
        .sort((a, b) => new Date(b.appointmentDate).getTime() - new Date(a.appointmentDate).getTime());
      
      const futureAppointments = appointments
        .filter(apt => new Date(apt.appointmentDate) > now)
        .sort((a, b) => new Date(a.appointmentDate).getTime() - new Date(b.appointmentDate).getTime());
      
      return {
        lastAppointment: pastAppointments[0],
        nextAppointment: futureAppointments[0],
        totalAppointments: appointments.length
      };
    } catch (error) {
      console.error('❌ Failed to fetch patient appointment summary:', error);
      return { totalAppointments: 0 };
    }
  }

  // 📝 Schedule new appointment in database
  async scheduleAppointment(doctorId: number, patientId: number, appointmentData: AppointmentRequest): Promise<AppointmentDTO> {
    console.log('🚀 Scheduling appointment in database:', { doctorId, patientId, appointmentData });
    
    try {
      const response = await api.post(`/doctor/${doctorId}/patients/${patientId}/appointments`, appointmentData);
      const appointment = response.data.data;
      
      console.log('✅ Appointment scheduled in database:', appointment);
      return appointment;
    } catch (error) {
      console.error('❌ Failed to schedule appointment in database:', error);
      throw error;
    }
  }

  // 🏥 Get patient's full appointment history from database
  async getPatientAppointments(patientId: number): Promise<AppointmentDTO[]> {
    console.log('🚀 Fetching patient appointments from database:', patientId);
    
    try {
      const response = await api.get(`/appointments/patient/${patientId}`);
      const appointments = response.data;
      
      console.log('✅ Patient appointments fetched from database:', appointments);
      return appointments;
    } catch (error) {
      console.error('❌ Failed to fetch patient appointments from database:', error);
      throw error;
    }
  }

  // 🔍 Check appointment slot availability in database
  async checkSlotAvailability(doctorId: number, appointmentDate: string, duration: number = 30): Promise<boolean> {
    try {
      const response = await api.get('/appointments/check-availability', {
        params: { doctorId, appointmentDate, duration }
      });
      return response.data.success;
    } catch (error) {
      console.error('❌ Failed to check slot availability:', error);
      return false;
    }
  }

  // 📅 Get doctor's available time slots from database
  async getDoctorAvailability(doctorId: number, date: string): Promise<string[]> {
    try {
      const response = await api.get(`/appointments/doctor/${doctorId}/availability`, {
        params: { date }
      });
      return response.data;
    } catch (error) {
      console.error('❌ Failed to fetch doctor availability:', error);
      return [];
    }
  }
}

export const doctorPatientsService = new DoctorPatientsService();
