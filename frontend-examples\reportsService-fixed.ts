// 📊 Fixed Reports Service - Matching Backend API Format
import { api } from '../api/api';

// 📋 Backend Response Types (matching actual API responses)
export interface UserGrowthResponse {
  monthlyGrowth: Record<string, number>; // { "2025-05": 10, "2025-04": 8, ... }
  totalUsers: number;
  activeUsers: number;
  roleDistribution: {
    DOCTOR: number;
    PATIENT: number;
    CLINIC: number;
    CLINIC_STAFF: number;
    ADMIN: number;
  };
}

export interface SystemStatsResponse {
  // User Statistics
  totalUsers: number;
  activeUsers: number;
  pendingUsers: number;
  inactiveUsers: number;
  suspendedUsers: number;
  
  // Role Distribution
  doctorCount: number;
  patientCount: number;
  clinicCount: number;
  clinicStaffCount: number;
  adminCount: number;
  
  // Growth Metrics
  newUsersThisMonth: number;
  newUsersLastMonth: number;
  userGrowthRate: number;
  
  // System Metrics
  systemUptime: string;
  apiResponseTime: string;
  activeSessions: number;
  lastBackup: string;
  databaseStatus: string;
  
  // Performance Metrics
  totalAppointments: number;
  completedAppointments: number;
  cancelledAppointments: number;
  appointmentCompletionRate: number;
  
  // Additional Statistics
  totalLogins: number;
  uniqueVisitors: number;
  peakUsageTime: string;
  storageUsed: number;
}

export interface PerformanceMetricsResponse {
  // System performance
  systemUptime: string;
  apiResponseTime: string;
  databaseResponseTime: string;
  memoryUsage: string;
  cpuUsage: string;
  diskUsage: string;
  
  // User activity
  activeUsersLast24h: number;
  totalSessions: number;
  averageSessionDuration: string;
  
  // Health metrics
  errorRate: string;
  successRate: string;
  healthStatus: string;
}

export interface AppointmentAnalyticsResponse {
  // Current metrics
  totalAppointments: number;
  completedAppointments: number;
  cancelledAppointments: number;
  pendingAppointments: number;
  
  // Weekly comparison
  appointmentsThisWeek: number;
  appointmentsLastWeek: number;
  appointmentGrowthRate: number;
  
  // Performance metrics
  averageAppointmentDuration: string;
  doctorUtilizationRate: string;
  patientSatisfactionScore: number;
  completionRate: number;
}

export interface RevenueAnalyticsResponse {
  totalRevenue: number;
  monthlyRevenue: number;
  averageRevenuePerUser: number;
  revenueGrowthRate: number;
  revenueBySource: {
    consultations: number;
    subscriptions: number;
    other: number;
  };
  profitMargin: string;
  operatingCosts: number;
  netProfit: number;
}

// 🔧 Fixed Reports Service
export class ReportsService {
  
  // 📊 Get system statistics (working endpoint)
  async getSystemStats(): Promise<SystemStatsResponse> {
    console.log('🚀 Fetching system statistics from backend...');
    
    try {
      const response = await api.get('/reports/system-stats');
      const stats = response.data;
      
      console.log('✅ System statistics fetched successfully:', stats);
      return stats;
    } catch (error) {
      console.error('❌ Backend API failed for system stats:', error);
      throw new Error('Failed to fetch system statistics');
    }
  }

  // 📈 Get user growth data (fixed to match backend format)
  async getUserGrowthData(): Promise<UserGrowthResponse> {
    console.log('🚀 Fetching user growth data from backend...');
    
    try {
      const response = await api.get('/reports/user-growth');
      const data = response.data;
      
      // Validate response format
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid response format: Expected object');
      }
      
      if (!data.monthlyGrowth || typeof data.monthlyGrowth !== 'object') {
        throw new Error('Invalid response format: Missing or invalid monthlyGrowth');
      }
      
      if (typeof data.totalUsers !== 'number') {
        throw new Error('Invalid response format: Missing or invalid totalUsers');
      }
      
      if (typeof data.activeUsers !== 'number') {
        throw new Error('Invalid response format: Missing or invalid activeUsers');
      }
      
      if (!data.roleDistribution || typeof data.roleDistribution !== 'object') {
        throw new Error('Invalid response format: Missing or invalid roleDistribution');
      }
      
      console.log('✅ User growth data fetched successfully:', data);
      return data as UserGrowthResponse;
    } catch (error) {
      console.error('❌ Backend API failed for user growth data:', error);
      
      // Return fallback data to prevent UI crashes
      const fallbackData: UserGrowthResponse = {
        monthlyGrowth: {},
        totalUsers: 0,
        activeUsers: 0,
        roleDistribution: {
          DOCTOR: 0,
          PATIENT: 0,
          CLINIC: 0,
          CLINIC_STAFF: 0,
          ADMIN: 0,
        }
      };
      
      console.log('🔄 Using fallback data for user growth:', fallbackData);
      return fallbackData;
    }
  }

  // 🚀 Get performance metrics
  async getPerformanceMetrics(): Promise<PerformanceMetricsResponse> {
    console.log('🚀 Fetching performance metrics from backend...');
    
    try {
      const response = await api.get('/reports/performance-metrics');
      const data = response.data;
      
      console.log('✅ Performance metrics fetched successfully:', data);
      return data;
    } catch (error) {
      console.error('❌ Backend API failed for performance metrics:', error);
      throw new Error('Failed to fetch performance metrics');
    }
  }

  // 📅 Get appointment analytics
  async getAppointmentAnalytics(): Promise<AppointmentAnalyticsResponse> {
    console.log('🚀 Fetching appointment analytics from backend...');
    
    try {
      const response = await api.get('/reports/appointment-analytics');
      const data = response.data;
      
      console.log('✅ Appointment analytics fetched successfully:', data);
      return data;
    } catch (error) {
      console.error('❌ Backend API failed for appointment analytics:', error);
      throw new Error('Failed to fetch appointment analytics');
    }
  }

  // 💰 Get revenue analytics
  async getRevenueAnalytics(): Promise<RevenueAnalyticsResponse> {
    console.log('🚀 Fetching revenue analytics from backend...');
    
    try {
      const response = await api.get('/reports/revenue-analytics');
      const data = response.data;
      
      console.log('✅ Revenue analytics fetched successfully:', data);
      return data;
    } catch (error) {
      console.error('❌ Backend API failed for revenue analytics:', error);
      throw new Error('Failed to fetch revenue analytics');
    }
  }

  // 📄 Export system overview as PDF
  async exportSystemOverviewPDF(): Promise<Blob> {
    console.log('🚀 Exporting system overview PDF...');
    
    try {
      const response = await api.get('/reports/export/system-overview', {
        responseType: 'blob'
      });
      
      console.log('✅ System overview PDF exported successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Failed to export system overview PDF:', error);
      throw new Error('Failed to export PDF report');
    }
  }

  // 👥 Export users report as PDF
  async exportUsersReportPDF(): Promise<Blob> {
    console.log('🚀 Exporting users report PDF...');
    
    try {
      const response = await api.get('/reports/export/users', {
        responseType: 'blob'
      });
      
      console.log('✅ Users report PDF exported successfully');
      return response.data;
    } catch (error) {
      console.error('❌ Failed to export users report PDF:', error);
      throw new Error('Failed to export users PDF report');
    }
  }

  // 🔄 Transform monthly growth data for charts
  transformMonthlyGrowthForChart(monthlyGrowth: Record<string, number>): Array<{
    month: string;
    users: number;
    label: string;
  }> {
    return Object.entries(monthlyGrowth)
      .sort(([a], [b]) => a.localeCompare(b)) // Sort by date
      .map(([month, users]) => ({
        month,
        users,
        label: new Date(month + '-01').toLocaleDateString('en-US', { 
          month: 'short', 
          year: 'numeric' 
        })
      }));
  }

  // 📊 Transform role distribution for charts
  transformRoleDistributionForChart(roleDistribution: UserGrowthResponse['roleDistribution']): Array<{
    role: string;
    count: number;
    label: string;
  }> {
    return Object.entries(roleDistribution).map(([role, count]) => ({
      role,
      count,
      label: role.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
    }));
  }
}

export const reportsService = new ReportsService();
