// Frontend Integration for Admin Settings Page
// This shows how to integrate with the backend settings API

import React, { useState, useEffect } from 'react';
import axios from 'axios';

interface SecuritySettings {
  twoFactorAuthEnabled: boolean;
  strongPasswordPolicyEnabled: boolean;
  sessionTimeoutMinutes: number;
  maxLoginAttempts: number;
  dataEncryptionEnabled: boolean;
  auditLoggingEnabled: boolean;
  passwordExpirationEnabled?: boolean;
  passwordExpirationDays?: number;
  accountLockoutEnabled?: boolean;
  lockoutDurationMinutes?: number;
  emailNotificationsEnabled?: boolean;
  securityAlertsEnabled?: boolean;
  loginNotificationsEnabled?: boolean;
  maintenanceModeEnabled?: boolean;
  maintenanceMessage?: string;
}

interface SecuritySettingsResponse extends SecuritySettings {
  securityLevel: string;
  securityScore: number;
  hasSecurityIssues: boolean;
  securityRecommendations: string[];
  lastUpdated: string;
}

const AdminSettingsPage: React.FC = () => {
  const [settings, setSettings] = useState<SecuritySettings>({
    twoFactorAuthEnabled: false,
    strongPasswordPolicyEnabled: true,
    sessionTimeoutMinutes: 30,
    maxLoginAttempts: 5,
    dataEncryptionEnabled: true,
    auditLoggingEnabled: true,
  });

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [securityStatus, setSecurityStatus] = useState<any>(null);

  const token = localStorage.getItem('authToken');

  // API configuration
  const api = axios.create({
    baseURL: '/api/admin/settings',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  // Load settings on component mount
  useEffect(() => {
    loadSettings();
    loadSecurityStatus();
  }, []);

  const loadSettings = async () => {
    setLoading(true);
    try {
      const response = await api.get<SecuritySettingsResponse>('/security');
      setSettings(response.data);
      setMessage(null);
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to load settings' });
      console.error('Error loading settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSecurityStatus = async () => {
    try {
      const response = await api.get('/security/status');
      setSecurityStatus(response.data);
    } catch (error) {
      console.error('Error loading security status:', error);
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    try {
      const response = await api.post('/security', settings);
      
      if (response.data.success) {
        setMessage({ type: 'success', text: 'Settings saved successfully!' });
        // Update settings with the response data
        if (response.data.data) {
          setSettings(response.data.data);
        }
        // Reload security status
        await loadSecurityStatus();
      } else {
        setMessage({ type: 'error', text: response.data.message || 'Failed to save settings' });
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || 'Failed to save settings';
      setMessage({ type: 'error', text: errorMessage });
      console.error('Error saving settings:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof SecuritySettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const testEmailConfiguration = async () => {
    try {
      const response = await api.post('/test-email');
      if (response.data.success) {
        setMessage({ type: 'success', text: 'Test email sent successfully!' });
      } else {
        setMessage({ type: 'error', text: 'Failed to send test email' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to send test email' });
    }
  };

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading settings...</div>;
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Admin Settings</h2>
          <p className="text-sm text-gray-600">Manage system security and access controls</p>
        </div>

        {/* Message Display */}
        {message && (
          <div className={`mx-6 mt-4 p-4 rounded-md ${
            message.type === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
          }`}>
            {message.text}
          </div>
        )}

        {/* Security Status */}
        {securityStatus && (
          <div className="mx-6 mt-4 p-4 bg-blue-50 rounded-md">
            <h3 className="text-sm font-medium text-blue-800">Security Status</h3>
            <div className="mt-2 flex items-center space-x-4">
              <span className={`px-2 py-1 text-xs rounded-full ${
                securityStatus.securityLevel === 'HIGH' ? 'bg-green-100 text-green-800' :
                securityStatus.securityLevel === 'MEDIUM' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {securityStatus.securityLevel} Security
              </span>
              <span className="text-sm text-blue-700">Score: {securityStatus.securityScore}/100</span>
            </div>
          </div>
        )}

        <div className="p-6 space-y-6">
          {/* Security Settings Section */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Security Settings</h3>
            <p className="text-sm text-gray-600 mb-6">Manage system security and access controls</p>

            <div className="space-y-4">
              {/* Authentication Settings */}
              <div>
                <h4 className="text-md font-medium text-gray-800 mb-3">Authentication Settings</h4>
                <p className="text-sm text-gray-600 mb-4">Configure user authentication and access policies</p>

                <div className="space-y-4">
                  {/* Two-Factor Authentication */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Two-Factor Authentication</label>
                      <p className="text-xs text-gray-500">Require 2FA for all admin accounts</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.twoFactorAuthEnabled}
                        onChange={(e) => handleInputChange('twoFactorAuthEnabled', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  {/* Strong Password Policy */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Strong Password Policy</label>
                      <p className="text-xs text-gray-500">Enforce complex password requirements</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.strongPasswordPolicyEnabled}
                        onChange={(e) => handleInputChange('strongPasswordPolicyEnabled', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  {/* Session Timeout */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Session Timeout (minutes)</label>
                      <p className="text-xs text-gray-500">Automatic logout after inactivity</p>
                    </div>
                    <input
                      type="number"
                      min="5"
                      max="480"
                      value={settings.sessionTimeoutMinutes}
                      onChange={(e) => handleInputChange('sessionTimeoutMinutes', parseInt(e.target.value))}
                      className="w-20 px-3 py-1 border border-gray-300 rounded-md text-sm"
                    />
                  </div>

                  {/* Max Login Attempts */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Max Login Attempts</label>
                      <p className="text-xs text-gray-500">Account lockout threshold</p>
                    </div>
                    <input
                      type="number"
                      min="3"
                      max="10"
                      value={settings.maxLoginAttempts}
                      onChange={(e) => handleInputChange('maxLoginAttempts', parseInt(e.target.value))}
                      className="w-20 px-3 py-1 border border-gray-300 rounded-md text-sm"
                    />
                  </div>
                </div>
              </div>

              {/* Data Security */}
              <div>
                <h4 className="text-md font-medium text-gray-800 mb-3">Data Security</h4>
                <p className="text-sm text-gray-600 mb-4">Configure data protection and monitoring</p>

                <div className="space-y-4">
                  {/* Data Encryption */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Data Encryption</label>
                      <p className="text-xs text-gray-500">Encrypt sensitive data at rest</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.dataEncryptionEnabled}
                        onChange={(e) => handleInputChange('dataEncryptionEnabled', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>

                  {/* Audit Logging */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Audit Logging</label>
                      <p className="text-xs text-gray-500">Log all admin and system actions</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.auditLoggingEnabled}
                        onChange={(e) => handleInputChange('auditLoggingEnabled', e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center pt-6 border-t border-gray-200">
            <div className="flex space-x-3">
              <button
                onClick={testEmailConfiguration}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Test Email
              </button>
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={loadSettings}
                disabled={loading}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
              >
                Reset
              </button>
              <button
                onClick={saveSettings}
                disabled={saving}
                className="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {saving ? 'Saving...' : 'Save Settings'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSettingsPage;
