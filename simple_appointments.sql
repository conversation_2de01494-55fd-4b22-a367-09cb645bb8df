-- Simple appointment creation script
-- First check what data we have
SELECT 'Checking existing data...' as status;
SELECT COUNT(*) as patient_count FROM patients;
SELECT COUNT(*) as doctor_count FROM doctors;
SELECT COUNT(*) as clinic_count FROM clinics;

-- Insert simple appointments
INSERT INTO appointments (patient_id, doctor_id, clinic_id, appointment_date, status, reason, duration_minutes, created_at, updated_at) VALUES

-- Past completed appointments (using correct IDs: patient=25, doctor=4, clinic=7)
(25, 4, 7, '2024-11-01 09:00:00', 'COMPLETED', 'Regular checkup', 30, '2024-10-28 10:00:00', '2024-11-01 09:30:00'),
(25, 4, 7, '2024-11-05 14:30:00', 'COMPLETED', 'Follow-up consultation', 45, '2024-11-02 11:00:00', '2024-11-05 15:15:00'),
(25, 4, 7, '2024-11-08 10:15:00', 'COMPLETED', 'Blood pressure monitoring', 30, '2024-11-05 16:00:00', '2024-11-08 10:45:00'),
(25, 4, 7, '2024-11-12 11:00:00', 'COMPLETED', 'Vaccination', 20, '2024-11-09 10:00:00', '2024-11-12 11:20:00'),
(25, 4, 7, '2024-11-15 15:30:00', 'COMPLETED', 'Allergy consultation', 40, '2024-11-12 14:00:00', '2024-11-15 16:10:00'),
(25, 4, 7, '2024-11-19 09:45:00', 'COMPLETED', 'Diabetes management', 45, '2024-11-16 12:00:00', '2024-11-19 10:30:00'),
(25, 4, 7, '2024-11-22 13:15:00', 'COMPLETED', 'Routine physical exam', 60, '2024-11-19 09:00:00', '2024-11-22 14:15:00'),
(25, 4, 7, '2024-11-26 10:30:00', 'COMPLETED', 'Medication review', 30, '2024-11-23 15:00:00', '2024-11-26 11:00:00'),
(25, 4, 7, '2024-11-29 14:00:00', 'COMPLETED', 'Lab results discussion', 30, '2024-11-26 11:00:00', '2024-11-29 14:30:00'),
(25, 4, 7, '2024-12-02 09:30:00', 'COMPLETED', 'Preventive care', 45, '2024-11-29 14:00:00', '2024-12-02 10:15:00'),
(25, 4, 7, '2024-12-05 11:15:00', 'COMPLETED', 'Headache assessment', 30, '2024-12-02 16:00:00', '2024-12-05 11:45:00'),
(25, 4, 7, '2024-12-09 15:00:00', 'COMPLETED', 'Annual wellness visit', 60, '2024-12-06 10:00:00', '2024-12-09 16:00:00'),
(25, 4, 7, '2024-12-12 10:00:00', 'COMPLETED', 'Symptom evaluation', 30, '2024-12-09 12:00:00', '2024-12-12 10:30:00'),
(25, 4, 7, '2024-12-16 14:30:00', 'COMPLETED', 'Treatment planning', 45, '2024-12-13 10:00:00', '2024-12-16 15:15:00'),
(25, 4, 7, '2024-12-19 11:00:00', 'COMPLETED', 'Health screening', 30, '2024-12-16 12:00:00', '2024-12-19 11:30:00'),

-- More appointments with different patients and doctors
(15, 21, 8, '2024-11-03 10:00:00', 'COMPLETED', 'Consultation', 30, '2024-10-31 10:00:00', '2024-11-03 10:30:00'),
(16, 22, 9, '2024-11-07 14:00:00', 'COMPLETED', 'Check-up', 45, '2024-11-04 11:00:00', '2024-11-07 14:45:00'),
(17, 23, 7, '2024-11-11 09:30:00', 'COMPLETED', 'Follow-up', 30, '2024-11-08 16:00:00', '2024-11-11 10:00:00'),
(18, 24, 8, '2024-11-14 16:00:00', 'COMPLETED', 'Treatment', 60, '2024-11-11 10:00:00', '2024-11-14 17:00:00'),

-- Past cancelled appointments
(25, 4, 7, '2024-11-18 13:00:00', 'CANCELLED', 'Skin examination', 30, '2024-11-15 12:00:00', '2024-11-18 08:00:00'),
(25, 4, 7, '2024-11-25 16:00:00', 'CANCELLED', 'Chest pain evaluation', 45, '2024-11-22 09:00:00', '2024-11-25 10:00:00'),
(15, 21, 8, '2024-12-07 10:30:00', 'CANCELLED', 'Routine visit', 30, '2024-12-04 14:00:00', '2024-12-07 08:00:00'),

-- Today's appointments
(25, 4, 7, CONCAT(CURDATE(), ' 09:00:00'), 'CONFIRMED', 'Morning consultation', 30, DATE_SUB(NOW(), INTERVAL 3 DAY), NOW()),
(15, 21, 8, CONCAT(CURDATE(), ' 14:00:00'), 'IN_PROGRESS', 'Afternoon checkup', 45, DATE_SUB(NOW(), INTERVAL 2 DAY), NOW()),
(16, 22, 9, CONCAT(CURDATE(), ' 16:30:00'), 'SCHEDULED', 'Evening consultation', 30, DATE_SUB(NOW(), INTERVAL 1 DAY), NOW()),

-- Future appointments
(25, 4, 7, DATE_ADD(CURDATE(), INTERVAL 1 DAY) + INTERVAL 10 HOUR, 'SCHEDULED', 'Follow-up appointment', 30, NOW(), NOW()),
(15, 21, 8, DATE_ADD(CURDATE(), INTERVAL 3 DAY) + INTERVAL 14 HOUR, 'CONFIRMED', 'Chronic condition management', 45, NOW(), NOW()),
(16, 22, 9, DATE_ADD(CURDATE(), INTERVAL 5 DAY) + INTERVAL 9 HOUR, 'SCHEDULED', 'Medication adjustment', 30, NOW(), NOW()),
(17, 23, 7, DATE_ADD(CURDATE(), INTERVAL 7 DAY) + INTERVAL 11 HOUR, 'CONFIRMED', 'Post-surgery follow-up', 30, NOW(), NOW()),
(18, 24, 8, DATE_ADD(CURDATE(), INTERVAL 10 DAY) + INTERVAL 15 HOUR, 'SCHEDULED', 'Emergency consultation', 45, NOW(), NOW()),
(25, 4, 7, DATE_ADD(CURDATE(), INTERVAL 14 DAY) + INTERVAL 10 HOUR, 'SCHEDULED', 'Routine checkup', 30, NOW(), NOW()),
(15, 21, 8, DATE_ADD(CURDATE(), INTERVAL 17 DAY) + INTERVAL 13 HOUR, 'CONFIRMED', 'Lab work review', 30, NOW(), NOW()),
(16, 22, 9, DATE_ADD(CURDATE(), INTERVAL 21 DAY) + INTERVAL 16 HOUR, 'SCHEDULED', 'Specialist referral', 45, NOW(), NOW()),
(17, 23, 7, DATE_ADD(CURDATE(), INTERVAL 25 DAY) + INTERVAL 11 HOUR, 'SCHEDULED', 'Quarterly review', 60, NOW(), NOW()),
(18, 24, 8, DATE_ADD(CURDATE(), INTERVAL 28 DAY) + INTERVAL 14 HOUR, 'CONFIRMED', 'Preventive screening', 45, NOW(), NOW());

-- Update cancelled appointments with cancellation details
UPDATE appointments SET
    cancelled_at = DATE_SUB(appointment_date, INTERVAL 2 HOUR),
    cancellation_reason = 'Patient illness'
WHERE status = 'CANCELLED' AND cancelled_at IS NULL;

-- Show results
SELECT 'Appointment creation completed!' as status;
SELECT
    status,
    COUNT(*) as count
FROM appointments
GROUP BY status
ORDER BY count DESC;

SELECT COUNT(*) as total_appointments FROM appointments;
