package com.example.MediConnect;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

@SpringBootApplication
@EnableJpaAuditing
public class MediConnectApplication {

	public static void main(String[] args) {
		SpringApplication.run(MediConnectApplication.class, args);
		System.out.println("MediConnect Application Started Successfully!");
		System.out.println("Server running on: http://localhost:8083");
		System.out.println("API Documentation available at: http://localhost:8083/api");
	}

}
