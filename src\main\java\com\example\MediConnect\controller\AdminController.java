package com.example.MediConnect.controller;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.example.MediConnect.dto.ClinicDTO;
import com.example.MediConnect.dto.DoctorDTO;
import com.example.MediConnect.dto.UserDTO;
import com.example.MediConnect.dto.request.AddUserRequest;
import com.example.MediConnect.dto.request.RoleChangeRequest;
import com.example.MediConnect.dto.response.ApiResponse;
import com.example.MediConnect.dto.response.SystemReportResponse;
import com.example.MediConnect.entity.Appointment;
import com.example.MediConnect.entity.Clinic;
import com.example.MediConnect.entity.Doctor;
import com.example.MediConnect.entity.Patient;
import com.example.MediConnect.enums.AppointmentStatus;
import com.example.MediConnect.enums.UserStatus;
import com.example.MediConnect.repository.AppointmentRepository;
import com.example.MediConnect.repository.ClinicRepository;
import com.example.MediConnect.repository.DoctorRepository;
import com.example.MediConnect.repository.PatientRepository;
import com.example.MediConnect.service.AdminService;
import com.example.MediConnect.service.SimplePdfReportService;
import com.example.MediConnect.util.Constants;

@RestController
@RequestMapping("/api/admin")
@CrossOrigin(origins = "*")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    @Autowired
    private AdminService adminService;

    @Autowired
    private SimplePdfReportService pdfReportService;

    @Autowired
    private AppointmentRepository appointmentRepository;

    @Autowired
    private PatientRepository patientRepository;

    @Autowired
    private DoctorRepository doctorRepository;

    @Autowired
    private ClinicRepository clinicRepository;

    // Approval Management
    @GetMapping("/clinics/pending")
    public ResponseEntity<List<ClinicDTO>> getPendingClinicApprovals() {
        List<ClinicDTO> pendingClinics = adminService.getPendingClinicApprovals();
        return ResponseEntity.ok(pendingClinics);
    }

    @PutMapping("/clinics/{id}/approve")
    public ResponseEntity<ApiResponse> approveClinic(@PathVariable Long id) {
        adminService.approveClinic(id);
        return ResponseEntity.ok(new ApiResponse(true, Constants.CLINIC_APPROVED_SUCCESSFULLY));
    }

    @PutMapping("/clinics/{id}/reject")
    public ResponseEntity<ApiResponse> rejectClinic(@PathVariable Long id) {
        adminService.rejectClinic(id);
        return ResponseEntity.ok(new ApiResponse(true, Constants.CLINIC_REJECTED_SUCCESSFULLY));
    }

    @GetMapping("/doctors/pending")
    public ResponseEntity<List<DoctorDTO>> getPendingDoctorApprovals() {
        List<DoctorDTO> pendingDoctors = adminService.getPendingDoctorApprovals();
        return ResponseEntity.ok(pendingDoctors);
    }

    @PutMapping("/doctors/{id}/approve")
    public ResponseEntity<ApiResponse> approveDoctor(@PathVariable Long id) {
        adminService.approveDoctor(id);
        return ResponseEntity.ok(new ApiResponse(true, Constants.DOCTOR_APPROVED_SUCCESSFULLY));
    }

    @PutMapping("/doctors/{id}/reject")
    public ResponseEntity<ApiResponse> rejectDoctor(@PathVariable Long id) {
        adminService.rejectDoctor(id);
        return ResponseEntity.ok(new ApiResponse(true, Constants.DOCTOR_REJECTED_SUCCESSFULLY));
    }

    // User Management
    @GetMapping("/users")
    public ResponseEntity<List<UserDTO>> getAllUsers() {
        List<UserDTO> users = adminService.getAllUsers();
        return ResponseEntity.ok(users);
    }

    @GetMapping("/users/{id}")
    public ResponseEntity<UserDTO> getUserById(@PathVariable Long id) {
        UserDTO user = adminService.getUserById(id);
        return ResponseEntity.ok(user);
    }

    @PostMapping("/users")
    public ResponseEntity<ApiResponse> addUser(@RequestBody AddUserRequest request) {
        try {
            UserDTO createdUser = adminService.addUser(request);
            return ResponseEntity.ok(new ApiResponse(true, "User created successfully", createdUser));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(new ApiResponse(false, e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(false, "Failed to create user: " + e.getMessage()));
        }
    }

    @PutMapping("/users/{id}/activate")
    public ResponseEntity<ApiResponse> activateUser(@PathVariable Long id) {
        adminService.activateUser(id);
        return ResponseEntity.ok(new ApiResponse(true, "User activated successfully"));
    }

    @PutMapping("/users/{id}/deactivate")
    public ResponseEntity<ApiResponse> deactivateUser(@PathVariable Long id) {
        adminService.deactivateUser(id);
        return ResponseEntity.ok(new ApiResponse(true, "User deactivated successfully"));
    }

    @PutMapping("/users/{id}/change-role")
    public ResponseEntity<ApiResponse> changeUserRole(@PathVariable Long id, @RequestParam String newRole) {
        try {
            adminService.changeUserRole(id, newRole);
            return ResponseEntity.ok(new ApiResponse(true, "User role changed successfully"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(new ApiResponse(false, e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(false, "Failed to change user role: " + e.getMessage()));
        }
    }

    @PutMapping("/users/{id}/role")
    public ResponseEntity<ApiResponse> changeUserRoleAlt(@PathVariable Long id, @RequestBody RoleChangeRequest request) {
        try {
            adminService.changeUserRole(id, request.getNewRole());
            return ResponseEntity.ok(new ApiResponse(true, "User role changed successfully"));
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(new ApiResponse(false, e.getMessage()));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(false, "Failed to change user role: " + e.getMessage()));
        }
    }

    @GetMapping("/users/status/{status}")
    public ResponseEntity<List<UserDTO>> getUsersByStatus(@PathVariable UserStatus status) {
        List<UserDTO> users = adminService.getUsersByStatus(status);
        return ResponseEntity.ok(users);
    }

    // System Reports
    @GetMapping("/reports/overview")
    public ResponseEntity<SystemReportResponse> getSystemOverview() {
        SystemReportResponse report = adminService.getSystemOverview();
        return ResponseEntity.ok(report);
    }

    @GetMapping("/reports/users")
    public ResponseEntity<SystemReportResponse> getUserStatistics() {
        SystemReportResponse report = adminService.getUserStatistics();
        return ResponseEntity.ok(report);
    }

    @GetMapping("/reports/appointments")
    public ResponseEntity<SystemReportResponse> getAppointmentStatistics() {
        SystemReportResponse report = adminService.getAppointmentStatistics();
        return ResponseEntity.ok(report);
    }

    // PDF Export Endpoints
    @GetMapping("/reports/export/system")
    public ResponseEntity<byte[]> exportSystemReport() {
        try {
            byte[] pdfBytes = pdfReportService.generateSystemOverviewReport();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", "system-report.pdf");
            headers.setContentLength(pdfBytes.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/reports/export/users")
    public ResponseEntity<byte[]> exportUsersReport() {
        try {
            byte[] pdfBytes = pdfReportService.generateUserReport();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", "users-report.pdf");
            headers.setContentLength(pdfBytes.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/reports/export/appointments")
    public ResponseEntity<byte[]> exportAppointmentsReport() {
        try {
            byte[] pdfBytes = pdfReportService.generateAppointmentReport();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", "appointments-report.pdf");
            headers.setContentLength(pdfBytes.length);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(pdfBytes);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    // Initialize Sample Data (for testing purposes)
    @PostMapping("/initialize-sample-data")
    public ResponseEntity<ApiResponse> initializeSampleData() {
        try {
            adminService.initializeSampleData();
            return ResponseEntity.ok(new ApiResponse(true, "Sample data initialized successfully"));
        } catch (Exception e) {
            return ResponseEntity.ok(new ApiResponse(false, "Failed to initialize sample data: " + e.getMessage()));
        }
    }

    // Create Sample Appointments (for testing purposes)
    @PostMapping("/create-sample-appointments")
    public ResponseEntity<ApiResponse> createSampleAppointments() {
        try {
            createAppointmentsInDatabase();
            return ResponseEntity.ok(new ApiResponse(true, "Sample appointments created successfully"));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(false, "Failed to create appointments: " + e.getMessage()));
        }
    }

    @GetMapping("/appointments/count")
    public ResponseEntity<ApiResponse> getAppointmentCount() {
        try {
            long count = appointmentRepository.count();
            return ResponseEntity.ok(new ApiResponse(true, "Total appointments: " + count));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(false, "Failed to get appointment count: " + e.getMessage()));
        }
    }

    private void createAppointmentsInDatabase() {
        // Get all available entities
        List<Patient> patients = patientRepository.findAll();
        List<Doctor> doctors = doctorRepository.findAll();
        List<Clinic> clinics = clinicRepository.findAll();

        if (patients.isEmpty() || doctors.isEmpty() || clinics.isEmpty()) {
            throw new RuntimeException("Not enough data to create appointments. Need patients, doctors, and clinics.");
        }

        Random random = new Random();

        // Create past completed appointments
        for (int i = 0; i < 15; i++) {
            LocalDateTime pastDate = LocalDateTime.now()
                .minusDays(random.nextInt(30) + 1)
                .withHour(9 + random.nextInt(8))
                .withMinute(random.nextBoolean() ? 0 : 30)
                .withSecond(0)
                .withNano(0);

            Patient patient = patients.get(random.nextInt(patients.size()));
            Doctor doctor = doctors.get(random.nextInt(doctors.size()));
            Clinic clinic = doctor.getClinic() != null ? doctor.getClinic() : clinics.get(0);

            AppointmentStatus status = random.nextDouble() < 0.8 ?
                AppointmentStatus.COMPLETED : AppointmentStatus.CANCELLED;

            createAndSaveAppointment(patient, doctor, clinic, pastDate, status, getRandomReason(), 30 + random.nextInt(31));
        }

        // Create future scheduled appointments
        for (int i = 0; i < 10; i++) {
            LocalDateTime futureDate = LocalDateTime.now()
                .plusDays(random.nextInt(30) + 1)
                .withHour(9 + random.nextInt(8))
                .withMinute(random.nextBoolean() ? 0 : 30)
                .withSecond(0)
                .withNano(0);

            Patient patient = patients.get(random.nextInt(patients.size()));
            Doctor doctor = doctors.get(random.nextInt(doctors.size()));
            Clinic clinic = doctor.getClinic() != null ? doctor.getClinic() : clinics.get(0);

            AppointmentStatus status = random.nextDouble() < 0.7 ?
                AppointmentStatus.SCHEDULED : AppointmentStatus.CONFIRMED;

            createAndSaveAppointment(patient, doctor, clinic, futureDate, status, getRandomReason(), 30 + random.nextInt(31));
        }

        // Create today's appointments
        for (int i = 0; i < 3; i++) {
            LocalDateTime todayDate = LocalDateTime.now()
                .withHour(9 + i * 3)
                .withMinute(random.nextBoolean() ? 0 : 30)
                .withSecond(0)
                .withNano(0);

            Patient patient = patients.get(random.nextInt(patients.size()));
            Doctor doctor = doctors.get(random.nextInt(doctors.size()));
            Clinic clinic = doctor.getClinic() != null ? doctor.getClinic() : clinics.get(0);

            AppointmentStatus status = i == 0 ? AppointmentStatus.IN_PROGRESS :
                (i == 1 ? AppointmentStatus.CONFIRMED : AppointmentStatus.SCHEDULED);

            createAndSaveAppointment(patient, doctor, clinic, todayDate, status, getRandomReason(), 30 + random.nextInt(31));
        }
    }

    private void createAndSaveAppointment(Patient patient, Doctor doctor, Clinic clinic,
                                        LocalDateTime appointmentDate, AppointmentStatus status,
                                        String reason, Integer duration) {
        Appointment appointment = new Appointment();
        appointment.setPatient(patient);
        appointment.setDoctor(doctor);
        appointment.setClinic(clinic);
        appointment.setAppointmentDate(appointmentDate);
        appointment.setStatus(status);
        appointment.setReason(reason);
        appointment.setDurationMinutes(duration);

        if (status == AppointmentStatus.CANCELLED) {
            appointment.setCancelledAt(appointmentDate.minusHours(2));
            appointment.setCancellationReason("Patient cancellation");
        }

        appointmentRepository.save(appointment);
    }

    private String getRandomReason() {
        String[] reasons = {
            "Regular checkup", "Follow-up consultation", "Skin examination",
            "Blood pressure monitoring", "Vaccination", "Allergy consultation",
            "Chest pain evaluation", "Headache assessment", "Diabetes management",
            "Routine physical exam", "Medication review", "Lab results discussion",
            "Preventive care", "Annual wellness visit", "Symptom evaluation"
        };
        return reasons[new Random().nextInt(reasons.length)];
    }
}
