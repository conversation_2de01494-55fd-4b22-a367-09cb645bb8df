package com.example.MediConnect.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*")
public class ApiDocumentationController {

    @GetMapping
    public ResponseEntity<Map<String, Object>> getApiDocumentation() {
        Map<String, Object> apiDoc = new HashMap<>();
        
        // Basic API Information
        apiDoc.put("title", "MediConnect Healthcare Management System API");
        apiDoc.put("version", "1.0.0");
        apiDoc.put("description", "Comprehensive Healthcare Management System REST API");
        apiDoc.put("baseUrl", "http://localhost:8083/api");
        
        // Authentication Information
        Map<String, Object> auth = new HashMap<>();
        auth.put("type", "Bearer Token (JWT)");
        auth.put("header", "Authorization: Bearer <token>");
        auth.put("loginEndpoint", "/api/auth/login");
        auth.put("registerEndpoint", "/api/auth/register");
        apiDoc.put("authentication", auth);
        
        // Sample Test Credentials
        Map<String, Object> testCredentials = new HashMap<>();
        testCredentials.put("admin", Map.of("email", "<EMAIL>", "password", "test123", "role", "ADMIN"));
        testCredentials.put("patient", Map.of("email", "<EMAIL>", "password", "test123", "role", "PATIENT"));
        apiDoc.put("testCredentials", testCredentials);
        
        // Available Endpoints
        Map<String, Object> endpoints = new HashMap<>();
        
        // Authentication Endpoints
        Map<String, Object> authEndpoints = new HashMap<>();
        authEndpoints.put("POST /api/auth/login", "User login - Returns JWT token");
        authEndpoints.put("POST /api/auth/register", "User registration");
        authEndpoints.put("POST /api/auth/logout", "User logout");
        authEndpoints.put("POST /api/auth/validate", "Validate JWT token");
        authEndpoints.put("POST /api/auth/refresh", "Refresh JWT token");
        endpoints.put("Authentication", authEndpoints);
        
        // Admin Endpoints
        Map<String, Object> adminEndpoints = new HashMap<>();
        adminEndpoints.put("GET /api/admin/reports/overview", "System statistics and overview");
        adminEndpoints.put("GET /api/admin/users", "Get all users with pagination");
        adminEndpoints.put("PUT /api/admin/users/{id}/activate", "Activate/deactivate user account");
        endpoints.put("Admin (Requires ADMIN role)", adminEndpoints);
        
        // Doctor Endpoints
        Map<String, Object> doctorEndpoints = new HashMap<>();
        doctorEndpoints.put("GET /api/doctors/profile", "Get doctor profile");
        doctorEndpoints.put("PUT /api/doctors/profile", "Update doctor profile");
        doctorEndpoints.put("GET /api/doctors/patients", "Get assigned patients");
        doctorEndpoints.put("GET /api/doctors/patients/{id}", "Get patient details");
        doctorEndpoints.put("POST /api/doctors/patients/{id}/diagnosis", "Log patient diagnosis");
        doctorEndpoints.put("GET /api/doctors/patients/{id}/history", "Get patient medical history");
        doctorEndpoints.put("POST /api/doctors/followups", "Schedule follow-up appointment");
        doctorEndpoints.put("GET /api/doctors/followups", "Get doctor's follow-ups");
        doctorEndpoints.put("PUT /api/doctors/followups/{id}", "Update follow-up");
        doctorEndpoints.put("DELETE /api/doctors/followups/{id}", "Cancel follow-up");
        doctorEndpoints.put("GET /api/doctors/dashboard", "Doctor dashboard statistics");
        endpoints.put("Doctor (Requires DOCTOR role)", doctorEndpoints);
        
        // Clinic Endpoints
        Map<String, Object> clinicEndpoints = new HashMap<>();
        clinicEndpoints.put("GET /api/clinics/profile", "Get clinic profile");
        clinicEndpoints.put("PUT /api/clinics/profile", "Update clinic profile");
        clinicEndpoints.put("GET /api/clinics/staff", "Get clinic staff");
        clinicEndpoints.put("POST /api/clinics/staff/doctors", "Add doctor to clinic");
        clinicEndpoints.put("DELETE /api/clinics/staff/doctors/{id}", "Remove doctor from clinic");
        clinicEndpoints.put("GET /api/clinics/appointments", "Get clinic appointments");
        clinicEndpoints.put("GET /api/clinics/appointments/today", "Get today's appointments");
        clinicEndpoints.put("GET /api/clinics/announcements", "Get clinic announcements");
        clinicEndpoints.put("POST /api/clinics/announcements", "Create announcement");
        clinicEndpoints.put("PUT /api/clinics/announcements/{id}", "Update announcement");
        clinicEndpoints.put("DELETE /api/clinics/announcements/{id}", "Delete announcement");
        clinicEndpoints.put("GET /api/clinics/dashboard", "Clinic dashboard statistics");
        endpoints.put("Clinic (Requires CLINIC role)", clinicEndpoints);
        
        // Appointment Endpoints
        Map<String, Object> appointmentEndpoints = new HashMap<>();
        appointmentEndpoints.put("POST /api/appointments", "Book new appointment");
        appointmentEndpoints.put("GET /api/appointments/{id}", "Get appointment details");
        appointmentEndpoints.put("PUT /api/appointments/{id}/cancel", "Cancel appointment");
        endpoints.put("Appointments (Requires authentication)", appointmentEndpoints);
        
        apiDoc.put("endpoints", endpoints);
        
        // Usage Examples
        Map<String, Object> examples = new HashMap<>();
        examples.put("login", Map.of(
            "method", "POST",
            "url", "/api/auth/login",
            "body", Map.of("email", "<EMAIL>", "password", "test123"),
            "response", "Returns JWT token for authentication"
        ));
        examples.put("getSystemOverview", Map.of(
            "method", "GET",
            "url", "/api/admin/reports/overview",
            "headers", Map.of("Authorization", "Bearer <your-jwt-token>"),
            "response", "Returns system statistics"
        ));
        apiDoc.put("examples", examples);
        
        // Additional Information
        Map<String, Object> info = new HashMap<>();
        info.put("status", "✅ Application Running");
        info.put("database", "✅ MySQL Connected");
        info.put("features", "✅ All Core Features Implemented");
        info.put("security", "✅ JWT Authentication & Role-based Authorization");
        info.put("notifications", "✅ Email Notifications Configured");
        info.put("testing", "✅ Unit Tests Available");
        apiDoc.put("systemStatus", info);
        
        return ResponseEntity.ok(apiDoc);
    }
    
    @GetMapping("/docs")
    public ResponseEntity<String> getSimpleDocumentation() {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>");
        html.append("<html><head><title>MediConnect API Documentation</title>");
        html.append("<style>body{font-family:Arial,sans-serif;margin:40px;} h1{color:#2c3e50;} h2{color:#34495e;} ");
        html.append("code{background:#f8f9fa;padding:2px 4px;border-radius:3px;} ");
        html.append(".endpoint{background:#e8f5e8;padding:10px;margin:10px 0;border-radius:5px;} ");
        html.append(".method{font-weight:bold;color:#27ae60;}</style></head><body>");
        
        html.append("<h1>🏥 MediConnect API Documentation</h1>");
        html.append("<p><strong>Base URL:</strong> <code>http://localhost:8083/api</code></p>");
        html.append("<p><strong>Status:</strong> ✅ Running and Functional</p>");
        
        html.append("<h2>🔐 Authentication</h2>");
        html.append("<p>All endpoints (except auth) require JWT token in Authorization header:</p>");
        html.append("<code>Authorization: Bearer &lt;your-jwt-token&gt;</code>");
        
        html.append("<h2>🧪 Test Credentials</h2>");
        html.append("<div class='endpoint'>");
        html.append("<strong>Admin:</strong> <EMAIL> / test123<br>");
        html.append("<strong>Patient:</strong> <EMAIL> / test123");
        html.append("</div>");
        
        html.append("<h2>📋 Quick Start</h2>");
        html.append("<div class='endpoint'>");
        html.append("<span class='method'>POST</span> <code>/api/auth/login</code><br>");
        html.append("Body: <code>{\"email\":\"<EMAIL>\",\"password\":\"test123\"}</code><br>");
        html.append("Returns JWT token for authentication");
        html.append("</div>");
        
        html.append("<h2>🔗 Main Endpoints</h2>");
        
        html.append("<h3>Authentication</h3>");
        html.append("<div class='endpoint'>");
        html.append("<span class='method'>POST</span> <code>/api/auth/login</code> - User login<br>");
        html.append("<span class='method'>POST</span> <code>/api/auth/register</code> - User registration<br>");
        html.append("<span class='method'>POST</span> <code>/api/auth/logout</code> - User logout");
        html.append("</div>");
        
        html.append("<h3>Admin (ADMIN role required)</h3>");
        html.append("<div class='endpoint'>");
        html.append("<span class='method'>GET</span> <code>/api/admin/reports/overview</code> - System statistics<br>");
        html.append("<span class='method'>GET</span> <code>/api/admin/users</code> - User management");
        html.append("</div>");
        
        html.append("<h3>Doctor (DOCTOR role required)</h3>");
        html.append("<div class='endpoint'>");
        html.append("<span class='method'>GET</span> <code>/api/doctors/profile</code> - Doctor profile<br>");
        html.append("<span class='method'>GET</span> <code>/api/doctors/patients</code> - Assigned patients<br>");
        html.append("<span class='method'>POST</span> <code>/api/doctors/patients/{id}/diagnosis</code> - Log diagnosis");
        html.append("</div>");
        
        html.append("<h3>Clinic (CLINIC role required)</h3>");
        html.append("<div class='endpoint'>");
        html.append("<span class='method'>GET</span> <code>/api/clinics/profile</code> - Clinic profile<br>");
        html.append("<span class='method'>GET</span> <code>/api/clinics/appointments</code> - Clinic appointments<br>");
        html.append("<span class='method'>POST</span> <code>/api/clinics/announcements</code> - Create announcements");
        html.append("</div>");
        
        html.append("<h3>Appointments (Authentication required)</h3>");
        html.append("<div class='endpoint'>");
        html.append("<span class='method'>POST</span> <code>/api/appointments</code> - Book appointment<br>");
        html.append("<span class='method'>GET</span> <code>/api/appointments/{id}</code> - Get appointment details");
        html.append("</div>");
        
        html.append("<h2>💡 Testing with curl</h2>");
        html.append("<div class='endpoint'>");
        html.append("<strong>1. Login:</strong><br>");
        html.append("<code>curl -X POST http://localhost:8083/api/auth/login -H \"Content-Type: application/json\" -d '{\"email\":\"<EMAIL>\",\"password\":\"test123\"}'</code><br><br>");
        html.append("<strong>2. Use token:</strong><br>");
        html.append("<code>curl -X GET http://localhost:8083/api/admin/reports/overview -H \"Authorization: Bearer YOUR_TOKEN\"</code>");
        html.append("</div>");
        
        html.append("<p><strong>For complete API details:</strong> <a href='/api'>GET /api</a> (JSON format)</p>");
        html.append("</body></html>");
        
        return ResponseEntity.ok()
                .header("Content-Type", "text/html")
                .body(html.toString());
    }
}
