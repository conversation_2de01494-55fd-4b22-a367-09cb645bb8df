package com.example.MediConnect.controller;

import com.example.MediConnect.dto.AppointmentDTO;
import com.example.MediConnect.dto.request.AppointmentRequest;
import com.example.MediConnect.dto.response.ApiResponse;
import com.example.MediConnect.enums.AppointmentStatus;
import com.example.MediConnect.service.AppointmentService;
import com.example.MediConnect.util.Constants;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/appointments")
@CrossOrigin(origins = "*")
public class AppointmentController {
    
    @Autowired
    private AppointmentService appointmentService;
    
    @PostMapping
    public ResponseEntity<AppointmentDTO> createAppointment(@Valid @RequestBody AppointmentRequest appointmentRequest) {
        AppointmentDTO appointment = appointmentService.createAppointment(appointmentRequest);
        return ResponseEntity.ok(appointment);
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<AppointmentDTO> getAppointmentById(@PathVariable Long id) {
        AppointmentDTO appointment = appointmentService.getAppointmentById(id);
        return ResponseEntity.ok(appointment);
    }
    
    @PutMapping("/{id}/status")
    public ResponseEntity<AppointmentDTO> updateAppointmentStatus(
            @PathVariable Long id, 
            @RequestParam AppointmentStatus status) {
        AppointmentDTO appointment = appointmentService.updateAppointmentStatus(id, status);
        return ResponseEntity.ok(appointment);
    }
    
    @GetMapping("/patient/{patientId}")
    public ResponseEntity<List<AppointmentDTO>> getAppointmentsByPatient(@PathVariable Long patientId) {
        List<AppointmentDTO> appointments = appointmentService.getAppointmentsByPatient(patientId);
        return ResponseEntity.ok(appointments);
    }
    
    @GetMapping("/doctor/{doctorId}")
    public ResponseEntity<List<AppointmentDTO>> getAppointmentsByDoctor(@PathVariable Long doctorId) {
        List<AppointmentDTO> appointments = appointmentService.getAppointmentsByDoctor(doctorId);
        return ResponseEntity.ok(appointments);
    }
    
    @GetMapping("/clinic/{clinicId}")
    public ResponseEntity<List<AppointmentDTO>> getAppointmentsByClinic(@PathVariable Long clinicId) {
        List<AppointmentDTO> appointments = appointmentService.getAppointmentsByClinic(clinicId);
        return ResponseEntity.ok(appointments);
    }
    
    @GetMapping("/date-range")
    public ResponseEntity<List<AppointmentDTO>> getAppointmentsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        List<AppointmentDTO> appointments = appointmentService.getAppointmentsByDateRange(startDate, endDate);
        return ResponseEntity.ok(appointments);
    }
    
    @GetMapping("/status/{status}")
    public ResponseEntity<List<AppointmentDTO>> getAppointmentsByStatus(@PathVariable AppointmentStatus status) {
        List<AppointmentDTO> appointments = appointmentService.getAppointmentsByStatus(status);
        return ResponseEntity.ok(appointments);
    }
    
    @PutMapping("/{id}/cancel")
    public ResponseEntity<ApiResponse> cancelAppointment(
            @PathVariable Long id, 
            @RequestParam(required = false) String reason) {
        appointmentService.cancelAppointment(id, reason);
        return ResponseEntity.ok(new ApiResponse(true, Constants.APPOINTMENT_CANCELLED_SUCCESSFULLY));
    }
    
    @GetMapping("/doctor/{doctorId}/availability")
    public ResponseEntity<List<String>> getDoctorAvailability(
            @PathVariable Long doctorId, 
            @RequestParam String date) {
        List<String> availableSlots = appointmentService.getAvailableSlots(doctorId, date);
        return ResponseEntity.ok(availableSlots);
    }
    
    @GetMapping("/check-availability")
    public ResponseEntity<ApiResponse> checkSlotAvailability(
            @RequestParam Long doctorId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime appointmentDate,
            @RequestParam(required = false) Integer duration) {
        boolean isAvailable = appointmentService.isSlotAvailable(doctorId, appointmentDate, duration);
        return ResponseEntity.ok(new ApiResponse(isAvailable, 
                isAvailable ? "Slot is available" : Constants.APPOINTMENT_SLOT_NOT_AVAILABLE));
    }
}
