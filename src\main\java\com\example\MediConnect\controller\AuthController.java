package com.example.MediConnect.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.example.MediConnect.dto.request.LoginRequest;
import com.example.MediConnect.dto.request.RegisterRequest;
import com.example.MediConnect.dto.response.ApiResponse;
import com.example.MediConnect.dto.response.AuthResponse;
import com.example.MediConnect.service.AuthService;
import com.example.MediConnect.util.Constants;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/auth")
@CrossOrigin(origins = "*")
public class AuthController {

    @Autowired
    private AuthService authService;

    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        AuthResponse response = authService.login(loginRequest);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/register")
    public ResponseEntity<AuthResponse> register(@Valid @RequestBody RegisterRequest registerRequest) {
        AuthResponse response = authService.register(registerRequest);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/logout")
    public ResponseEntity<ApiResponse> logout(@RequestHeader("Authorization") String token) {
        String jwtToken = token.substring(7); // Remove "Bearer " prefix
        authService.logout(jwtToken);
        return ResponseEntity.ok(new ApiResponse(true, Constants.LOGOUT_SUCCESSFUL));
    }

    @PostMapping("/validate")
    public ResponseEntity<ApiResponse> validateToken(@RequestHeader("Authorization") String token) {
        String jwtToken = token.substring(7); // Remove "Bearer " prefix
        boolean isValid = authService.validateToken(jwtToken);
        return ResponseEntity.ok(new ApiResponse(isValid, isValid ? "Token is valid" : "Token is invalid"));
    }

    @PostMapping("/refresh")
    public ResponseEntity<ApiResponse> refreshToken(@RequestHeader("Authorization") String token) {
        String jwtToken = token.substring(7); // Remove "Bearer " prefix
        String newToken = authService.refreshToken(jwtToken);
        return ResponseEntity.ok(new ApiResponse(true, "Token refreshed successfully", newToken));
    }
}
