package com.example.MediConnect.controller;

import com.example.MediConnect.dto.*;
import com.example.MediConnect.dto.request.AnnouncementRequest;
import com.example.MediConnect.dto.request.DoctorRequest;
import com.example.MediConnect.dto.response.ApiResponse;
import com.example.MediConnect.entity.User;
import com.example.MediConnect.service.ClinicService;
import com.example.MediConnect.util.Constants;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/clinics")
@CrossOrigin(origins = "*")
@PreAuthorize("hasRole('CLINIC')")
public class ClinicController {
    
    @Autowired
    private ClinicService clinicService;
    
    // Profile Management
    @GetMapping("/profile")
    public ResponseEntity<ClinicDTO> getClinicProfile(@RequestParam Long clinicId) {
        ClinicDTO clinic = clinicService.getClinicProfile(clinicId);
        return ResponseEntity.ok(clinic);
    }
    
    @PutMapping("/profile")
    public ResponseEntity<ClinicDTO> updateClinicProfile(
            @RequestParam Long clinicId, 
            @Valid @RequestBody ClinicDTO clinicDTO) {
        ClinicDTO updatedClinic = clinicService.updateClinicProfile(clinicId, clinicDTO);
        return ResponseEntity.ok(updatedClinic);
    }
    
    // Staff Management
    @GetMapping("/staff")
    public ResponseEntity<List<User>> getClinicStaff(@RequestParam Long clinicId) {
        List<User> staff = clinicService.getClinicStaff(clinicId);
        return ResponseEntity.ok(staff);
    }
    
    @PostMapping("/staff/doctors")
    public ResponseEntity<DoctorDTO> addDoctorToClinic(
            @RequestParam Long clinicId, 
            @Valid @RequestBody DoctorRequest doctorRequest) {
        DoctorDTO doctor = clinicService.addDoctorToClinic(clinicId, doctorRequest);
        return ResponseEntity.ok(doctor);
    }
    
    @DeleteMapping("/staff/doctors/{id}")
    public ResponseEntity<ApiResponse> removeDoctorFromClinic(
            @RequestParam Long clinicId, 
            @PathVariable Long id) {
        clinicService.removeDoctorFromClinic(clinicId, id);
        return ResponseEntity.ok(new ApiResponse(true, "Doctor removed from clinic successfully"));
    }
    
    @PostMapping("/staff/staff")
    public ResponseEntity<User> addClinicStaff(
            @RequestParam Long clinicId, 
            @Valid @RequestBody User staffMember) {
        User staff = clinicService.addClinicStaff(clinicId, staffMember);
        return ResponseEntity.ok(staff);
    }
    
    @DeleteMapping("/staff/staff/{id}")
    public ResponseEntity<ApiResponse> removeClinicStaff(
            @RequestParam Long clinicId, 
            @PathVariable Long id) {
        clinicService.removeClinicStaff(clinicId, id);
        return ResponseEntity.ok(new ApiResponse(true, "Staff member removed successfully"));
    }
    
    // Appointment Management
    @GetMapping("/appointments")
    public ResponseEntity<List<AppointmentDTO>> getClinicAppointments(@RequestParam Long clinicId) {
        List<AppointmentDTO> appointments = clinicService.getClinicAppointments(clinicId);
        return ResponseEntity.ok(appointments);
    }
    
    @GetMapping("/appointments/upcoming")
    public ResponseEntity<List<AppointmentDTO>> getUpcomingAppointments(@RequestParam Long clinicId) {
        List<AppointmentDTO> appointments = clinicService.getUpcomingAppointments(clinicId);
        return ResponseEntity.ok(appointments);
    }
    
    @GetMapping("/appointments/today")
    public ResponseEntity<List<AppointmentDTO>> getTodayAppointments(@RequestParam Long clinicId) {
        List<AppointmentDTO> appointments = clinicService.getTodayAppointments(clinicId);
        return ResponseEntity.ok(appointments);
    }
    
    // Announcements
    @GetMapping("/announcements")
    public ResponseEntity<List<AnnouncementDTO>> getClinicAnnouncements(@RequestParam Long clinicId) {
        List<AnnouncementDTO> announcements = clinicService.getClinicAnnouncements(clinicId);
        return ResponseEntity.ok(announcements);
    }
    
    @PostMapping("/announcements")
    public ResponseEntity<AnnouncementDTO> createAnnouncement(
            @RequestParam Long clinicId, 
            @Valid @RequestBody AnnouncementRequest announcementRequest) {
        AnnouncementDTO announcement = clinicService.createAnnouncement(clinicId, announcementRequest);
        return ResponseEntity.ok(announcement);
    }
    
    @PutMapping("/announcements/{id}")
    public ResponseEntity<AnnouncementDTO> updateAnnouncement(
            @PathVariable Long id, 
            @Valid @RequestBody AnnouncementRequest announcementRequest) {
        AnnouncementDTO announcement = clinicService.updateAnnouncement(id, announcementRequest);
        return ResponseEntity.ok(announcement);
    }
    
    @DeleteMapping("/announcements/{id}")
    public ResponseEntity<ApiResponse> deleteAnnouncement(@PathVariable Long id) {
        clinicService.deleteAnnouncement(id);
        return ResponseEntity.ok(new ApiResponse(true, Constants.ANNOUNCEMENT_DELETED_SUCCESSFULLY));
    }
    
    // Dashboard and Statistics
    @GetMapping("/dashboard")
    public ResponseEntity<ClinicDashboardDTO> getClinicDashboard(@RequestParam Long clinicId) {
        ClinicDashboardDTO dashboard = new ClinicDashboardDTO();
        dashboard.setClinicId(clinicId);
        dashboard.setTotalStaff(clinicService.getClinicStaff(clinicId).size());
        dashboard.setTotalAppointments(clinicService.getClinicAppointments(clinicId).size());
        dashboard.setTodayAppointments(clinicService.getTodayAppointments(clinicId).size());
        dashboard.setUpcomingAppointments(clinicService.getUpcomingAppointments(clinicId).size());
        dashboard.setActiveAnnouncements(clinicService.getClinicAnnouncements(clinicId).size());
        return ResponseEntity.ok(dashboard);
    }
    
    // Helper DTO for dashboard
    public static class ClinicDashboardDTO {
        private Long clinicId;
        private Integer totalStaff;
        private Integer totalDoctors;
        private Integer totalAppointments;
        private Integer todayAppointments;
        private Integer upcomingAppointments;
        private Integer activeAnnouncements;
        
        // Getters and setters
        public Long getClinicId() { return clinicId; }
        public void setClinicId(Long clinicId) { this.clinicId = clinicId; }
        
        public Integer getTotalStaff() { return totalStaff; }
        public void setTotalStaff(Integer totalStaff) { this.totalStaff = totalStaff; }
        
        public Integer getTotalDoctors() { return totalDoctors; }
        public void setTotalDoctors(Integer totalDoctors) { this.totalDoctors = totalDoctors; }
        
        public Integer getTotalAppointments() { return totalAppointments; }
        public void setTotalAppointments(Integer totalAppointments) { this.totalAppointments = totalAppointments; }
        
        public Integer getTodayAppointments() { return todayAppointments; }
        public void setTodayAppointments(Integer todayAppointments) { this.todayAppointments = todayAppointments; }
        
        public Integer getUpcomingAppointments() { return upcomingAppointments; }
        public void setUpcomingAppointments(Integer upcomingAppointments) { this.upcomingAppointments = upcomingAppointments; }
        
        public Integer getActiveAnnouncements() { return activeAnnouncements; }
        public void setActiveAnnouncements(Integer activeAnnouncements) { this.activeAnnouncements = activeAnnouncements; }
    }
}
