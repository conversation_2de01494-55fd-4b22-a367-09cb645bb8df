package com.example.MediConnect.controller;

import com.example.MediConnect.dto.*;
import com.example.MediConnect.dto.request.DiagnosisRequest;
import com.example.MediConnect.dto.request.DoctorRequest;
import com.example.MediConnect.dto.request.FollowUpRequest;
import com.example.MediConnect.dto.response.ApiResponse;
import com.example.MediConnect.service.DoctorService;
import com.example.MediConnect.util.Constants;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/doctors")
@CrossOrigin(origins = "*")
@PreAuthorize("hasRole('DOCTOR')")
public class DoctorController {
    
    @Autowired
    private DoctorService doctorService;
    
    // Profile Management
    @GetMapping("/profile")
    public ResponseEntity<DoctorDTO> getDoctorProfile(@RequestParam Long doctorId) {
        DoctorDTO doctor = doctorService.getDoctorProfile(doctorId);
        return ResponseEntity.ok(doctor);
    }
    
    @PutMapping("/profile")
    public ResponseEntity<DoctorDTO> updateDoctorProfile(
            @RequestParam Long doctorId, 
            @Valid @RequestBody DoctorRequest doctorRequest) {
        DoctorDTO updatedDoctor = doctorService.updateDoctorProfile(doctorId, doctorRequest);
        return ResponseEntity.ok(updatedDoctor);
    }
    
    // Patient Management
    @GetMapping("/patients")
    public ResponseEntity<List<PatientDTO>> getAssignedPatients(@RequestParam Long doctorId) {
        List<PatientDTO> patients = doctorService.getAssignedPatients(doctorId);
        return ResponseEntity.ok(patients);
    }
    
    @GetMapping("/patients/{id}")
    public ResponseEntity<PatientDTO> getPatientDetails(@PathVariable Long id) {
        PatientDTO patient = doctorService.getPatientDetails(id);
        return ResponseEntity.ok(patient);
    }
    
    @PostMapping("/patients/{id}/diagnosis")
    public ResponseEntity<DiagnosisDTO> logPatientDiagnosis(
            @PathVariable Long id, 
            @Valid @RequestBody DiagnosisRequest diagnosisRequest) {
        // Ensure the patient ID matches the path variable
        diagnosisRequest.setPatientId(id);
        DiagnosisDTO diagnosis = doctorService.logPatientDiagnosis(diagnosisRequest);
        return ResponseEntity.ok(diagnosis);
    }
    
    @GetMapping("/patients/{id}/history")
    public ResponseEntity<List<DiagnosisDTO>> getPatientMedicalHistory(@PathVariable Long id) {
        List<DiagnosisDTO> medicalHistory = doctorService.getPatientMedicalHistory(id);
        return ResponseEntity.ok(medicalHistory);
    }
    
    // Follow-up Management
    @PostMapping("/followups")
    public ResponseEntity<FollowUpDTO> scheduleFollowUp(@Valid @RequestBody FollowUpRequest followUpRequest) {
        FollowUpDTO followUp = doctorService.scheduleFollowUp(followUpRequest);
        return ResponseEntity.ok(followUp);
    }
    
    @GetMapping("/followups")
    public ResponseEntity<List<FollowUpDTO>> getScheduledFollowUps(@RequestParam Long doctorId) {
        List<FollowUpDTO> followUps = doctorService.getScheduledFollowUps(doctorId);
        return ResponseEntity.ok(followUps);
    }
    
    @PutMapping("/followups/{id}")
    public ResponseEntity<FollowUpDTO> updateFollowUp(
            @PathVariable Long id, 
            @Valid @RequestBody FollowUpRequest followUpRequest) {
        FollowUpDTO updatedFollowUp = doctorService.updateFollowUp(id, followUpRequest);
        return ResponseEntity.ok(updatedFollowUp);
    }
    
    @DeleteMapping("/followups/{id}")
    public ResponseEntity<ApiResponse> cancelFollowUp(@PathVariable Long id) {
        doctorService.cancelFollowUp(id);
        return ResponseEntity.ok(new ApiResponse(true, Constants.FOLLOWUP_CANCELLED_SUCCESSFULLY));
    }
    
    // Statistics and Reports
    @GetMapping("/dashboard")
    public ResponseEntity<DoctorDashboardDTO> getDoctorDashboard(@RequestParam Long doctorId) {
        // This would be implemented to provide dashboard statistics
        DoctorDashboardDTO dashboard = new DoctorDashboardDTO();
        dashboard.setDoctorId(doctorId);
        dashboard.setTotalPatients(doctorService.getAssignedPatients(doctorId).size());
        dashboard.setUpcomingFollowUps(doctorService.getScheduledFollowUps(doctorId).size());
        return ResponseEntity.ok(dashboard);
    }
    
    // Helper DTO for dashboard
    public static class DoctorDashboardDTO {
        private Long doctorId;
        private Integer totalPatients;
        private Integer upcomingFollowUps;
        private Integer todayAppointments;
        private Integer completedDiagnoses;
        
        // Getters and setters
        public Long getDoctorId() { return doctorId; }
        public void setDoctorId(Long doctorId) { this.doctorId = doctorId; }
        
        public Integer getTotalPatients() { return totalPatients; }
        public void setTotalPatients(Integer totalPatients) { this.totalPatients = totalPatients; }
        
        public Integer getUpcomingFollowUps() { return upcomingFollowUps; }
        public void setUpcomingFollowUps(Integer upcomingFollowUps) { this.upcomingFollowUps = upcomingFollowUps; }
        
        public Integer getTodayAppointments() { return todayAppointments; }
        public void setTodayAppointments(Integer todayAppointments) { this.todayAppointments = todayAppointments; }
        
        public Integer getCompletedDiagnoses() { return completedDiagnoses; }
        public void setCompletedDiagnoses(Integer completedDiagnoses) { this.completedDiagnoses = completedDiagnoses; }
    }
}
