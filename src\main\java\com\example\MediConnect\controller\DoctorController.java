package com.example.MediConnect.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.example.MediConnect.dto.DiagnosisDTO;
import com.example.MediConnect.dto.DoctorDTO;
import com.example.MediConnect.dto.FollowUpDTO;
import com.example.MediConnect.dto.PatientDTO;
import com.example.MediConnect.dto.request.DiagnosisRequest;
import com.example.MediConnect.dto.request.DoctorRequest;
import com.example.MediConnect.dto.request.FollowUpRequest;
import com.example.MediConnect.dto.response.ApiResponse;
import com.example.MediConnect.service.DoctorService;
import com.example.MediConnect.util.Constants;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/api/doctors")
@CrossOrigin(origins = "*")
@PreAuthorize("hasRole('DOCTOR')")
public class DoctorController {
    
    @Autowired
    private DoctorService doctorService;
    
    // Profile Management
    @GetMapping("/profile")
    public ResponseEntity<DoctorDTO> getDoctorProfile(@RequestParam Long doctorId) {
        DoctorDTO doctor = doctorService.getDoctorProfile(doctorId);
        return ResponseEntity.ok(doctor);
    }
    
    @PutMapping("/profile")
    public ResponseEntity<DoctorDTO> updateDoctorProfile(
            @RequestParam Long doctorId, 
            @Valid @RequestBody DoctorRequest doctorRequest) {
        DoctorDTO updatedDoctor = doctorService.updateDoctorProfile(doctorId, doctorRequest);
        return ResponseEntity.ok(updatedDoctor);
    }
    
    // Patient Management
    @GetMapping("/patients")
    public ResponseEntity<List<PatientDTO>> getAssignedPatients(@RequestParam Long doctorId) {
        List<PatientDTO> patients = doctorService.getAssignedPatients(doctorId);
        return ResponseEntity.ok(patients);
    }
    
    @GetMapping("/patients/{id}")
    public ResponseEntity<PatientDTO> getPatientDetails(@PathVariable Long id) {
        PatientDTO patient = doctorService.getPatientDetails(id);
        return ResponseEntity.ok(patient);
    }
    
    @PostMapping("/patients/{id}/diagnosis")
    public ResponseEntity<DiagnosisDTO> logPatientDiagnosis(
            @PathVariable Long id, 
            @Valid @RequestBody DiagnosisRequest diagnosisRequest) {
        // Ensure the patient ID matches the path variable
        diagnosisRequest.setPatientId(id);
        DiagnosisDTO diagnosis = doctorService.logPatientDiagnosis(diagnosisRequest);
        return ResponseEntity.ok(diagnosis);
    }
    
    @GetMapping("/patients/{id}/history")
    public ResponseEntity<List<DiagnosisDTO>> getPatientMedicalHistory(@PathVariable Long id) {
        List<DiagnosisDTO> medicalHistory = doctorService.getPatientMedicalHistory(id);
        return ResponseEntity.ok(medicalHistory);
    }
    
    // Follow-up Management
    @PostMapping("/followups")
    public ResponseEntity<FollowUpDTO> scheduleFollowUp(@Valid @RequestBody FollowUpRequest followUpRequest) {
        FollowUpDTO followUp = doctorService.scheduleFollowUp(followUpRequest);
        return ResponseEntity.ok(followUp);
    }
    
    @GetMapping("/followups")
    public ResponseEntity<List<FollowUpDTO>> getScheduledFollowUps(@RequestParam Long doctorId) {
        List<FollowUpDTO> followUps = doctorService.getScheduledFollowUps(doctorId);
        return ResponseEntity.ok(followUps);
    }
    
    @PutMapping("/followups/{id}")
    public ResponseEntity<FollowUpDTO> updateFollowUp(
            @PathVariable Long id, 
            @Valid @RequestBody FollowUpRequest followUpRequest) {
        FollowUpDTO updatedFollowUp = doctorService.updateFollowUp(id, followUpRequest);
        return ResponseEntity.ok(updatedFollowUp);
    }
    
    @DeleteMapping("/followups/{id}")
    public ResponseEntity<ApiResponse> cancelFollowUp(@PathVariable Long id) {
        doctorService.cancelFollowUp(id);
        return ResponseEntity.ok(new ApiResponse(true, Constants.FOLLOWUP_CANCELLED_SUCCESSFULLY));
    }
    
    // Statistics and Reports
    @GetMapping("/dashboard")
    public ResponseEntity<DoctorDashboardDTO> getDoctorDashboard(@RequestParam Long doctorId) {
        // This would be implemented to provide dashboard statistics
        DoctorDashboardDTO dashboard = new DoctorDashboardDTO();
        dashboard.setDoctorId(doctorId);
        dashboard.setTotalPatients(doctorService.getAssignedPatients(doctorId).size());
        dashboard.setUpcomingFollowUps(doctorService.getScheduledFollowUps(doctorId).size());
        return ResponseEntity.ok(dashboard);
    }
    
    // Helper DTO for dashboard
    public static class DoctorDashboardDTO {
        private Long doctorId;
        private Integer totalPatients;
        private Integer upcomingFollowUps;
        private Integer todayAppointments;
        private Integer completedDiagnoses;
        
        // Getters and setters
        public Long getDoctorId() { return doctorId; }
        public void setDoctorId(Long doctorId) { this.doctorId = doctorId; }
        
        public Integer getTotalPatients() { return totalPatients; }
        public void setTotalPatients(Integer totalPatients) { this.totalPatients = totalPatients; }
        
        public Integer getUpcomingFollowUps() { return upcomingFollowUps; }
        public void setUpcomingFollowUps(Integer upcomingFollowUps) { this.upcomingFollowUps = upcomingFollowUps; }
        
        public Integer getTodayAppointments() { return todayAppointments; }
        public void setTodayAppointments(Integer todayAppointments) { this.todayAppointments = todayAppointments; }
        
        public Integer getCompletedDiagnoses() { return completedDiagnoses; }
        public void setCompletedDiagnoses(Integer completedDiagnoses) { this.completedDiagnoses = completedDiagnoses; }
    }
}

// Additional controller for frontend compatibility
@RestController
@RequestMapping("/api/doctor")
@CrossOrigin(origins = "*")
@PreAuthorize("hasRole('DOCTOR') or hasRole('ADMIN')")
class DoctorPatientsController {

    @Autowired
    private DoctorService doctorService;

    // Frontend expects: GET /api/doctor/{doctorId}/patients
    @GetMapping("/{doctorId}/patients")
    public ResponseEntity<ApiResponse> getDoctorPatients(@PathVariable Long doctorId) {
        try {
            List<PatientDTO> patients = doctorService.getAssignedPatients(doctorId);
            return ResponseEntity.ok(new ApiResponse(true, "Patients retrieved successfully", patients));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(false, "Failed to retrieve patients: " + e.getMessage()));
        }
    }

    // Frontend expects: GET /api/doctor/{doctorId}/patients/stats
    @GetMapping("/{doctorId}/patients/stats")
    public ResponseEntity<ApiResponse> getDoctorPatientStats(@PathVariable Long doctorId) {
        try {
            List<PatientDTO> patients = doctorService.getAssignedPatients(doctorId);

            // Calculate statistics
            int totalPatients = patients.size();
            int activePatients = totalPatients; // Assume all patients are active for now
            int newPatients = (int) patients.stream()
                .filter(p -> p.getCreatedAt() != null &&
                    p.getCreatedAt().isAfter(java.time.LocalDateTime.now().minusDays(30)))
                .count();

            // Create stats response
            java.util.Map<String, Object> stats = new java.util.HashMap<>();
            stats.put("totalPatients", totalPatients);
            stats.put("activePatients", activePatients);
            stats.put("newPatients", newPatients);
            stats.put("inactivePatients", totalPatients - activePatients);
            stats.put("averageAge", calculateAverageAge(patients));
            stats.put("genderDistribution", calculateGenderDistribution(patients));

            return ResponseEntity.ok(new ApiResponse(true, "Patient statistics retrieved successfully", stats));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(false, "Failed to retrieve patient statistics: " + e.getMessage()));
        }
    }

    private double calculateAverageAge(List<PatientDTO> patients) {
        return patients.stream()
            .filter(p -> p.getDateOfBirth() != null && !p.getDateOfBirth().isEmpty())
            .mapToInt(p -> {
                try {
                    java.time.LocalDate birthDate = java.time.LocalDate.parse(p.getDateOfBirth());
                    return java.time.Period.between(birthDate, java.time.LocalDate.now()).getYears();
                } catch (Exception e) {
                    return 0; // Default age if parsing fails
                }
            })
            .average()
            .orElse(0.0);
    }

    private java.util.Map<String, Integer> calculateGenderDistribution(List<PatientDTO> patients) {
        java.util.Map<String, Integer> distribution = new java.util.HashMap<>();
        distribution.put("male", 0);
        distribution.put("female", 0);
        distribution.put("other", 0);

        for (PatientDTO patient : patients) {
            String gender = patient.getGender();
            if (gender != null) {
                switch (gender.toLowerCase()) {
                    case "male":
                        distribution.put("male", distribution.get("male") + 1);
                        break;
                    case "female":
                        distribution.put("female", distribution.get("female") + 1);
                        break;
                    default:
                        distribution.put("other", distribution.get("other") + 1);
                        break;
                }
            }
        }

        return distribution;
    }
}
