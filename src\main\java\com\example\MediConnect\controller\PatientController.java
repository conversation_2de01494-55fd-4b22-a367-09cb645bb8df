package com.example.MediConnect.controller;

import com.example.MediConnect.dto.*;
import com.example.MediConnect.dto.request.AppointmentRequest;
import com.example.MediConnect.dto.response.ApiResponse;
import com.example.MediConnect.entity.Speciality;
import com.example.MediConnect.enums.Specialization;
import com.example.MediConnect.service.PatientService;
import com.example.MediConnect.util.Constants;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/patients")
@CrossOrigin(origins = "*")
@PreAuthorize("hasRole('PATIENT')")
public class PatientController {

    @Autowired
    private PatientService patientService;

    // Profile Management
    @GetMapping("/profile")
    public ResponseEntity<PatientDTO> getPatientProfile(@RequestParam Long patientId) {
        PatientDTO patient = patientService.getPatientProfile(patientId);
        return ResponseEntity.ok(patient);
    }

    @PutMapping("/profile")
    public ResponseEntity<PatientDTO> updatePatientProfile(
            @RequestParam Long patientId,
            @Valid @RequestBody PatientDTO patientDTO) {
        PatientDTO updatedPatient = patientService.updatePatientProfile(patientId, patientDTO);
        return ResponseEntity.ok(updatedPatient);
    }

    // Appointment Booking
    @PostMapping("/appointments")
    public ResponseEntity<AppointmentDTO> bookAppointment(@Valid @RequestBody AppointmentRequest appointmentRequest) {
        AppointmentDTO appointment = patientService.bookAppointment(appointmentRequest);
        return ResponseEntity.ok(appointment);
    }

    @GetMapping("/appointments")
    public ResponseEntity<List<AppointmentDTO>> getPatientAppointments(@RequestParam Long patientId) {
        List<AppointmentDTO> appointments = patientService.getPatientAppointments(patientId);
        return ResponseEntity.ok(appointments);
    }

    @GetMapping("/appointments/history")
    public ResponseEntity<List<AppointmentDTO>> getAppointmentHistory(@RequestParam Long patientId) {
        List<AppointmentDTO> appointments = patientService.getAppointmentHistory(patientId);
        return ResponseEntity.ok(appointments);
    }

    @PutMapping("/appointments/{id}/cancel")
    public ResponseEntity<ApiResponse> cancelAppointment(@PathVariable Long id) {
        patientService.cancelAppointment(id);
        return ResponseEntity.ok(new ApiResponse(true, Constants.APPOINTMENT_CANCELLED_SUCCESSFULLY));
    }

    // Search Functionality
    @GetMapping("/search/doctors")
    public ResponseEntity<List<DoctorDTO>> searchDoctorsBySpecialty(@RequestParam String specialty) {
        try {
            Specialization specialization = Specialization.valueOf(specialty.toUpperCase());
            List<DoctorDTO> doctors = patientService.searchDoctorsBySpecialty(specialization);
            return ResponseEntity.ok(doctors);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid specialty: " + specialty + ". Valid specialties are: " +
                String.join(", ", java.util.Arrays.stream(Specialization.values())
                    .map(Enum::name).toArray(String[]::new)));
        }
    }

    @GetMapping("/search/clinics")
    public ResponseEntity<List<ClinicDTO>> searchClinicsBySpecialty(@RequestParam String specialty) {
        try {
            Specialization specialization = Specialization.valueOf(specialty.toUpperCase());
            List<ClinicDTO> clinics = patientService.searchClinicsBySpecialty(specialization);
            return ResponseEntity.ok(clinics);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid specialty: " + specialty + ". Valid specialties are: " +
                String.join(", ", java.util.Arrays.stream(Specialization.values())
                    .map(Enum::name).toArray(String[]::new)));
        }
    }

    @GetMapping("/doctors/{id}/availability")
    public ResponseEntity<List<String>> getDoctorAvailability(
            @PathVariable Long id,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        List<String> availability = patientService.getDoctorAvailability(id, date);
        return ResponseEntity.ok(availability);
    }

    // Medical History
    @GetMapping("/medical-history")
    public ResponseEntity<List<DiagnosisDTO>> getMedicalHistory(@RequestParam Long patientId) {
        List<DiagnosisDTO> medicalHistory = patientService.getMedicalHistory(patientId);
        return ResponseEntity.ok(medicalHistory);
    }

    @GetMapping("/diagnoses")
    public ResponseEntity<List<DiagnosisDTO>> getDiagnoses(@RequestParam Long patientId) {
        List<DiagnosisDTO> diagnoses = patientService.getDiagnoses(patientId);
        return ResponseEntity.ok(diagnoses);
    }

    @GetMapping("/prescriptions")
    public ResponseEntity<List<PrescriptionDTO>> getPrescriptions(@RequestParam Long patientId) {
        List<PrescriptionDTO> prescriptions = patientService.getPrescriptions(patientId);
        return ResponseEntity.ok(prescriptions);
    }
}
