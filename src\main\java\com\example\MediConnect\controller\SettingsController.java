package com.example.MediConnect.controller;

import com.example.MediConnect.dto.request.SecuritySettingsRequest;
import com.example.MediConnect.dto.response.ApiResponse;
import com.example.MediConnect.dto.response.SecuritySettingsResponse;
import com.example.MediConnect.service.SystemSettingsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/settings")
@CrossOrigin(origins = "*")
@PreAuthorize("hasRole('ADMIN')")
public class SettingsController {

    @Autowired
    private SystemSettingsService systemSettingsService;

    @GetMapping("/security")
    public ResponseEntity<SecuritySettingsResponse> getSecuritySettings() {
        try {
            SecuritySettingsResponse settings = systemSettingsService.getSecuritySettings();
            return ResponseEntity.ok(settings);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/security")
    public ResponseEntity<ApiResponse> saveSecuritySettings(
            @Valid @RequestBody SecuritySettingsRequest request,
            Authentication authentication) {
        try {
            String updatedBy = authentication.getName();
            SecuritySettingsResponse updatedSettings = systemSettingsService.saveSecuritySettings(request, updatedBy);
            
            return ResponseEntity.ok(new ApiResponse(
                true, 
                "Security settings saved successfully", 
                updatedSettings
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(
                false, 
                "Failed to save security settings: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/security/status")
    public ResponseEntity<Map<String, Object>> getSecurityStatus() {
        try {
            SecuritySettingsResponse settings = systemSettingsService.getSecuritySettings();
            
            Map<String, Object> status = new HashMap<>();
            status.put("securityLevel", settings.getSecurityLevel());
            status.put("securityScore", settings.getSecurityScore());
            status.put("hasSecurityIssues", settings.getHasSecurityIssues());
            status.put("recommendations", settings.getSecurityRecommendations());
            status.put("lastUpdated", settings.getLastUpdated());
            
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/initialize-defaults")
    public ResponseEntity<ApiResponse> initializeDefaultSettings() {
        try {
            systemSettingsService.initializeDefaultSettings();
            return ResponseEntity.ok(new ApiResponse(true, "Default settings initialized successfully"));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(
                false, 
                "Failed to initialize default settings: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/system-info")
    public ResponseEntity<Map<String, Object>> getSystemInfo() {
        try {
            Map<String, Object> systemInfo = new HashMap<>();
            
            // System information
            systemInfo.put("version", "1.0.0");
            systemInfo.put("environment", "production");
            systemInfo.put("uptime", "99.9%");
            systemInfo.put("lastBackup", "2024-12-27 02:00:00");
            
            // Database information
            systemInfo.put("databaseStatus", "healthy");
            systemInfo.put("databaseSize", "2.5 GB");
            systemInfo.put("activeConnections", 15);
            
            // Security information
            SecuritySettingsResponse securitySettings = systemSettingsService.getSecuritySettings();
            systemInfo.put("securityLevel", securitySettings.getSecurityLevel());
            systemInfo.put("securityScore", securitySettings.getSecurityScore());
            
            return ResponseEntity.ok(systemInfo);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/backup-status")
    public ResponseEntity<Map<String, Object>> getBackupStatus() {
        try {
            Map<String, Object> backupStatus = new HashMap<>();
            
            backupStatus.put("lastBackup", "2024-12-27 02:00:00");
            backupStatus.put("nextScheduledBackup", "2024-12-28 02:00:00");
            backupStatus.put("backupSize", "2.1 GB");
            backupStatus.put("backupLocation", "/backups/mediconnect/");
            backupStatus.put("autoBackupEnabled", true);
            backupStatus.put("retentionDays", 30);
            
            return ResponseEntity.ok(backupStatus);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/test-email")
    public ResponseEntity<ApiResponse> testEmailConfiguration() {
        try {
            // This would normally send a test email
            // For now, we'll just return a success response
            return ResponseEntity.ok(new ApiResponse(true, "Test email sent successfully"));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(
                false, 
                "Failed to send test email: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/audit-logs")
    public ResponseEntity<Map<String, Object>> getAuditLogsSummary() {
        try {
            Map<String, Object> auditSummary = new HashMap<>();
            
            auditSummary.put("totalLogs", 1250);
            auditSummary.put("logsToday", 45);
            auditSummary.put("criticalEvents", 2);
            auditSummary.put("warningEvents", 8);
            auditSummary.put("lastCriticalEvent", "2024-12-26 14:30:00");
            auditSummary.put("logRetentionDays", 90);
            
            return ResponseEntity.ok(auditSummary);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
}
