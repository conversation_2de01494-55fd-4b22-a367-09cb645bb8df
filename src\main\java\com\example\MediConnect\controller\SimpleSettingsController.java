package com.example.MediConnect.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.example.MediConnect.dto.response.ApiResponse;

@RestController
@RequestMapping("/api/admin/simple-settings")
@CrossOrigin(origins = "*")
@PreAuthorize("hasRole('ADMIN')")
public class SimpleSettingsController {

    // In-memory storage for demo purposes (you can replace with database later)
    private static Map<String, Object> settings = new HashMap<>();
    
    static {
        // Initialize default settings
        settings.put("twoFactorAuthEnabled", false);
        settings.put("strongPasswordPolicyEnabled", true);
        settings.put("sessionTimeoutMinutes", 30);
        settings.put("maxLoginAttempts", 5);
        settings.put("dataEncryptionEnabled", true);
        settings.put("auditLoggingEnabled", true);
    }

    @GetMapping("/security")
    public ResponseEntity<Map<String, Object>> getSecuritySettings() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("twoFactorAuthEnabled", settings.get("twoFactorAuthEnabled"));
            response.put("strongPasswordPolicyEnabled", settings.get("strongPasswordPolicyEnabled"));
            response.put("sessionTimeoutMinutes", settings.get("sessionTimeoutMinutes"));
            response.put("maxLoginAttempts", settings.get("maxLoginAttempts"));
            response.put("dataEncryptionEnabled", settings.get("dataEncryptionEnabled"));
            response.put("auditLoggingEnabled", settings.get("auditLoggingEnabled"));
            
            // Add security metrics
            response.put("securityLevel", calculateSecurityLevel());
            response.put("securityScore", calculateSecurityScore());
            response.put("lastUpdated", java.time.LocalDateTime.now().toString());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/security")
    public ResponseEntity<ApiResponse> saveSecuritySettings(
            @RequestBody Map<String, Object> request,
            Authentication authentication) {
        try {
            String updatedBy = authentication.getName();
            
            // Update settings
            if (request.containsKey("twoFactorAuthEnabled")) {
                settings.put("twoFactorAuthEnabled", request.get("twoFactorAuthEnabled"));
            }
            if (request.containsKey("strongPasswordPolicyEnabled")) {
                settings.put("strongPasswordPolicyEnabled", request.get("strongPasswordPolicyEnabled"));
            }
            if (request.containsKey("sessionTimeoutMinutes")) {
                settings.put("sessionTimeoutMinutes", request.get("sessionTimeoutMinutes"));
            }
            if (request.containsKey("maxLoginAttempts")) {
                settings.put("maxLoginAttempts", request.get("maxLoginAttempts"));
            }
            if (request.containsKey("dataEncryptionEnabled")) {
                settings.put("dataEncryptionEnabled", request.get("dataEncryptionEnabled"));
            }
            if (request.containsKey("auditLoggingEnabled")) {
                settings.put("auditLoggingEnabled", request.get("auditLoggingEnabled"));
            }
            
            // Add metadata
            settings.put("lastUpdatedBy", updatedBy);
            settings.put("lastUpdated", java.time.LocalDateTime.now().toString());
            
            // Create response with updated settings
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("twoFactorAuthEnabled", settings.get("twoFactorAuthEnabled"));
            responseData.put("strongPasswordPolicyEnabled", settings.get("strongPasswordPolicyEnabled"));
            responseData.put("sessionTimeoutMinutes", settings.get("sessionTimeoutMinutes"));
            responseData.put("maxLoginAttempts", settings.get("maxLoginAttempts"));
            responseData.put("dataEncryptionEnabled", settings.get("dataEncryptionEnabled"));
            responseData.put("auditLoggingEnabled", settings.get("auditLoggingEnabled"));
            responseData.put("securityLevel", calculateSecurityLevel());
            responseData.put("securityScore", calculateSecurityScore());
            responseData.put("lastUpdated", settings.get("lastUpdated"));
            responseData.put("lastUpdatedBy", settings.get("lastUpdatedBy"));
            
            return ResponseEntity.ok(new ApiResponse(
                true, 
                "Security settings saved successfully", 
                responseData
            ));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(
                false, 
                "Failed to save security settings: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/security/status")
    public ResponseEntity<Map<String, Object>> getSecurityStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("securityLevel", calculateSecurityLevel());
            status.put("securityScore", calculateSecurityScore());
            status.put("hasSecurityIssues", calculateSecurityScore() < 70);
            status.put("recommendations", getSecurityRecommendations());
            status.put("lastUpdated", settings.get("lastUpdated"));
            
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @PostMapping("/test-email")
    public ResponseEntity<ApiResponse> testEmailConfiguration() {
        try {
            // Simulate email test
            return ResponseEntity.ok(new ApiResponse(true, "Test email sent successfully"));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(
                false, 
                "Failed to send test email: " + e.getMessage()
            ));
        }
    }

    @GetMapping("/system-info")
    public ResponseEntity<Map<String, Object>> getSystemInfo() {
        try {
            Map<String, Object> systemInfo = new HashMap<>();
            
            // System information
            systemInfo.put("version", "1.0.0");
            systemInfo.put("environment", "production");
            systemInfo.put("uptime", "99.9%");
            systemInfo.put("lastBackup", "2024-12-27 02:00:00");
            
            // Database information
            systemInfo.put("databaseStatus", "healthy");
            systemInfo.put("databaseSize", "2.5 GB");
            systemInfo.put("activeConnections", 15);
            
            // Security information
            systemInfo.put("securityLevel", calculateSecurityLevel());
            systemInfo.put("securityScore", calculateSecurityScore());
            
            return ResponseEntity.ok(systemInfo);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }

    // Helper methods
    private String calculateSecurityLevel() {
        int score = calculateSecurityScore();
        if (score >= 80) return "HIGH";
        else if (score >= 60) return "MEDIUM";
        else return "LOW";
    }

    private int calculateSecurityScore() {
        int score = 0;
        
        if ((Boolean) settings.get("twoFactorAuthEnabled")) score += 20;
        if ((Boolean) settings.get("strongPasswordPolicyEnabled")) score += 15;
        if ((Boolean) settings.get("dataEncryptionEnabled")) score += 20;
        if ((Boolean) settings.get("auditLoggingEnabled")) score += 15;
        
        Integer sessionTimeout = (Integer) settings.get("sessionTimeoutMinutes");
        if (sessionTimeout != null && sessionTimeout <= 30) score += 10;
        
        Integer maxAttempts = (Integer) settings.get("maxLoginAttempts");
        if (maxAttempts != null && maxAttempts <= 5) score += 10;
        
        // Base score for having settings configured
        score += 10;
        
        return Math.min(score, 100);
    }

    private String[] getSecurityRecommendations() {
        java.util.List<String> recommendations = new java.util.ArrayList<>();
        
        if (!(Boolean) settings.get("twoFactorAuthEnabled")) {
            recommendations.add("Enable two-factor authentication for enhanced security");
        }
        if (!(Boolean) settings.get("strongPasswordPolicyEnabled")) {
            recommendations.add("Enable strong password policy");
        }
        if (!(Boolean) settings.get("dataEncryptionEnabled")) {
            recommendations.add("Enable data encryption at rest");
        }
        if (!(Boolean) settings.get("auditLoggingEnabled")) {
            recommendations.add("Enable audit logging for compliance");
        }
        
        Integer sessionTimeout = (Integer) settings.get("sessionTimeoutMinutes");
        if (sessionTimeout != null && sessionTimeout > 30) {
            recommendations.add("Consider reducing session timeout to 30 minutes or less");
        }
        
        Integer maxAttempts = (Integer) settings.get("maxLoginAttempts");
        if (maxAttempts != null && maxAttempts > 5) {
            recommendations.add("Consider reducing max login attempts to 5 or less");
        }
        
        return recommendations.toArray(new String[0]);
    }
}
