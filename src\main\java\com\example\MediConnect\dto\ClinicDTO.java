package com.example.MediConnect.dto;

import com.example.MediConnect.enums.ClinicStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClinicDTO {
    private Long id;
    private String name;
    private String email;
    private String phoneNumber;
    private String address;
    private String clinicName;
    private String licenseNumber;
    private String description;
    private ClinicStatus clinicStatus;
    private String operatingHours;
    private String emergencyContact;
    private String websiteUrl;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Integer totalDoctors;
    private Integer totalStaff;
}
