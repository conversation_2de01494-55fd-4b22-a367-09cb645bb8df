package com.example.MediConnect.dto;

import com.example.MediConnect.enums.Role;
import com.example.MediConnect.enums.UserStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserDTO {
    
    private Long id;
    private String name;
    private String email;
    private String phoneNumber;
    private String address;
    private String dateOfBirth;
    private String gender;
    private Role role;
    private UserStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime lastLogin;
    
    // Additional fields for specific roles
    private String employeeId; // For Admin and ClinicStaff
    private String department; // For Admin
    private String accessLevel; // For Admin
    private String medicalLicense; // For Doctor
    private String specialtyName; // For Doctor
    private Integer yearsOfExperience; // For Doctor
    private String qualification; // For Doctor
    private String doctorStatus; // For Doctor
    private Long clinicId; // For Doctor and ClinicStaff
    private String clinicName; // For Doctor and ClinicStaff
    private Double consultationFee; // For Doctor
    private String bio; // For Doctor
    private String patientId; // For Patient
    private String emergencyContactName; // For Patient
    private String emergencyContactPhone; // For Patient
    private String bloodGroup; // For Patient
    private String allergies; // For Patient
    private String medicalHistory; // For Patient
    private String insuranceProvider; // For Patient
    private String insuranceNumber; // For Patient
    private String clinicLicenseNumber; // For Clinic
    private String description; // For Clinic
    private String clinicStatus; // For Clinic
    private String operatingHours; // For Clinic
    private String emergencyContact; // For Clinic
    private String websiteUrl; // For Clinic
    private String position; // For ClinicStaff
    private String hireDate; // For ClinicStaff
    private Double salary; // For ClinicStaff
    private Boolean isActive; // For ClinicStaff
}
