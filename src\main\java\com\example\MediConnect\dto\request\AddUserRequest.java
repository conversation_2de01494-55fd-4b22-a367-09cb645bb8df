package com.example.MediConnect.dto.request;

import com.example.MediConnect.enums.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

// import jakarta.validation.constraints.Email;
// import jakarta.validation.constraints.NotBlank;
// import jakarta.validation.constraints.NotNull;
// import jakarta.validation.constraints.Size;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddUserRequest {

    // Basic Information
    private String name;
    private String email;
    private String password;

    // Contact Information
    private String phoneNumber;
    private String dateOfBirth; // Format: mm/dd/yyyy or yyyy-MM-dd
    private String address;
    private String gender; // MALE, FEMALE, OTHER

    // Role Assignment
    private Role role;

    // Role-specific fields (optional, will be set to defaults if not provided)

    // For Doctor role
    private String medicalLicense;
    private Integer yearsOfExperience;
    private String qualification;
    private Double consultationFee;
    private String bio;
    private Long specialityId;
    private Long clinicId;

    // For Patient role
    private String emergencyContactName;
    private String emergencyContactPhone;
    private String bloodGroup;
    private String allergies;
    private String medicalHistory;
    private String insuranceProvider;
    private String insuranceNumber;

    // For Clinic role
    private String clinicName;
    private String licenseNumber;
    private String description;
    private String operatingHours;
    private String emergencyContact;
    private String websiteUrl;

    // For Admin role
    private String employeeId;
    private String department;
    private String accessLevel;

    // For ClinicStaff role
    private String position;
    private String hireDate;
    private Double salary;
    private Boolean isActive;
}
