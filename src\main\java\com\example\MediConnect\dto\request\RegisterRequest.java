package com.example.MediConnect.dto.request;

import com.example.MediConnect.enums.Role;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RegisterRequest {
    
    @NotBlank(message = "Name is required")
    private String name;
    
    @NotBlank(message = "Email is required")
    @Email(message = "Email should be valid")
    private String email;
    
    @NotBlank(message = "Password is required")
    private String password;
    
    @NotNull(message = "Role is required")
    private Role role;
    
    private String phoneNumber;
    private String address;
    private String dateOfBirth;
    private String gender;
    
    // Doctor specific fields
    private String medicalLicense;
    private String specialization;
    private Integer yearsOfExperience;
    private String qualification;
    private Double consultationFee;
    private String bio;
    private Long clinicId;
    
    // Clinic specific fields
    private String clinicName;
    private String licenseNumber;
    private String description;
    private String operatingHours;
    private String emergencyContact;
    private String websiteUrl;
    
    // Patient specific fields
    private String emergencyContactName;
    private String emergencyContactPhone;
    private String bloodGroup;
    private String allergies;
    private String medicalHistory;
    private String insuranceProvider;
    private String insuranceNumber;
    
    // Admin specific fields
    private String employeeId;
    private String department;
    private String accessLevel;
    
    // Clinic Staff specific fields
    private String position;
    private String hireDate;
    private Double salary;
}
