package com.example.MediConnect.dto.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SecuritySettingsRequest {

    // Authentication Settings
    @NotNull(message = "Two-factor authentication setting is required")
    private Boolean twoFactorAuthEnabled;

    @NotNull(message = "Strong password policy setting is required")
    private Boolean strongPasswordPolicyEnabled;

    @NotNull(message = "Session timeout is required")
    @Min(value = 5, message = "Session timeout must be at least 5 minutes")
    @Max(value = 480, message = "Session timeout cannot exceed 8 hours (480 minutes)")
    private Integer sessionTimeoutMinutes;

    @NotNull(message = "Max login attempts is required")
    @Min(value = 3, message = "Max login attempts must be at least 3")
    @Max(value = 10, message = "Max login attempts cannot exceed 10")
    private Integer maxLoginAttempts;

    // Data Security Settings
    @NotNull(message = "Data encryption setting is required")
    private Boolean dataEncryptionEnabled;

    @NotNull(message = "Audit logging setting is required")
    private Boolean auditLoggingEnabled;

    // Additional security settings
    private Boolean passwordExpirationEnabled;
    private Integer passwordExpirationDays;
    private Boolean accountLockoutEnabled;
    private Integer lockoutDurationMinutes;
    private Boolean ipWhitelistEnabled;
    private String allowedIpAddresses;

    // Email notification settings
    private Boolean emailNotificationsEnabled;
    private Boolean securityAlertsEnabled;
    private Boolean loginNotificationsEnabled;

    // System maintenance settings
    private Boolean maintenanceModeEnabled;
    private String maintenanceMessage;

}
