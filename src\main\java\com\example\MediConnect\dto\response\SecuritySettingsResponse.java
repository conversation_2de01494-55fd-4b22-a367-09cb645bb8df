package com.example.MediConnect.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SecuritySettingsResponse {
    
    // Authentication Settings
    private Boolean twoFactorAuthEnabled;
    private Boolean strongPasswordPolicyEnabled;
    private Integer sessionTimeoutMinutes;
    private Integer maxLoginAttempts;
    
    // Data Security Settings
    private Boolean dataEncryptionEnabled;
    private Boolean auditLoggingEnabled;
    
    // Additional security settings
    private Boolean passwordExpirationEnabled;
    private Integer passwordExpirationDays;
    private Boolean accountLockoutEnabled;
    private Integer lockoutDurationMinutes;
    private Boolean ipWhitelistEnabled;
    private String allowedIpAddresses;
    
    // Email notification settings
    private Boolean emailNotificationsEnabled;
    private Boolean securityAlertsEnabled;
    private Boolean loginNotificationsEnabled;
    
    // System maintenance settings
    private Boolean maintenanceModeEnabled;
    private String maintenanceMessage;
    
    // Metadata
    private LocalDateTime lastUpdated;
    private String lastUpdatedBy;
    private String version;
    
    // Security status indicators
    private String securityLevel; // LOW, MEDIUM, HIGH
    private Integer securityScore; // 0-100
    private Boolean hasSecurityIssues;
    private String[] securityRecommendations;
    
}
