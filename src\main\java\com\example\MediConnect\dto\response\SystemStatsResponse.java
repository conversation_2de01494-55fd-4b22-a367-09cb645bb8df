package com.example.MediConnect.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SystemStatsResponse {
    
    // User Statistics
    private Long totalUsers;
    private Long activeUsers;
    private Long pendingUsers;
    private Long inactiveUsers;
    private Long suspendedUsers;
    
    // Role Distribution
    private Long doctorCount;
    private Long patientCount;
    private Long clinicCount;
    private Long clinicStaffCount;
    private Long adminCount;
    
    // System Metrics
    private String systemUptime;
    private String apiResponseTime;
    private Long activeSessions;
    private String lastBackup;
    private String databaseStatus;
    
    // Growth Metrics
    private Long newUsersThisMonth;
    private Long newUsersLastMonth;
    private Double userGrowthRate;
    
    // Performance Metrics
    private Long totalAppointments;
    private Long completedAppointments;
    private Long cancelledAppointments;
    private Double appointmentCompletionRate;
    
    // Additional Statistics
    private Long totalLogins;
    private Long uniqueVisitors;
    private String peakUsageTime;
    private Long storageUsed; // in MB
    
}
