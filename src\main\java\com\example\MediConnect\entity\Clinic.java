package com.example.MediConnect.entity;

import com.example.MediConnect.enums.ClinicStatus;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "clinics")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@PrimaryKeyJoinColumn(name = "user_id")
public class Clinic extends User {
    
    @Column(name = "clinic_name", nullable = false)
    private String clinicName;
    
    @Column(name = "license_number", unique = true)
    private String licenseNumber;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "clinic_status")
    private ClinicStatus clinicStatus = ClinicStatus.PENDING_APPROVAL;
    
    @Column(name = "operating_hours")
    private String operatingHours;
    
    @Column(name = "emergency_contact")
    private String emergencyContact;
    
    @Column(name = "website_url")
    private String websiteUrl;
    
    @OneToMany(mappedBy = "clinic", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Doctor> doctors;
    
    @OneToMany(mappedBy = "clinic", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ClinicStaff> clinicStaff;
    
    @OneToMany(mappedBy = "clinic", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Announcement> announcements;
}
