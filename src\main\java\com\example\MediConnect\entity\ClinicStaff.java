package com.example.MediConnect.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "clinic_staff")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@PrimaryKeyJoinColumn(name = "user_id")
public class ClinicStaff extends User {
    
    @Column(name = "employee_id", unique = true)
    private String employeeId;
    
    @Column(name = "position")
    private String position;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "clinic_id", nullable = false)
    private Clinic clinic;
    
    @Column(name = "hire_date")
    private String hireDate;
    
    @Column(name = "salary")
    private Double salary;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
}
