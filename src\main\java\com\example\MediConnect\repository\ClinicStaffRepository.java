package com.example.MediConnect.repository;

import com.example.MediConnect.entity.ClinicStaff;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ClinicStaffRepository extends JpaRepository<ClinicStaff, Long> {
    
    Optional<ClinicStaff> findByEmail(String email);
    
    Optional<ClinicStaff> findByEmployeeId(String employeeId);
    
    boolean existsByEmployeeId(String employeeId);
    
    List<ClinicStaff> findByClinicId(Long clinicId);
    
    List<ClinicStaff> findByClinicIdAndIsActive(Long clinicId, Boolean isActive);
}
