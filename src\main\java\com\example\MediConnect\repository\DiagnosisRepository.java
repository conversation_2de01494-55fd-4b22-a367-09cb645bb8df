package com.example.MediConnect.repository;

import com.example.MediConnect.entity.Diagnosis;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DiagnosisRepository extends JpaRepository<Diagnosis, Long> {
    
    List<Diagnosis> findByPatientId(Long patientId);
    
    List<Diagnosis> findByDoctorId(Long doctorId);
    
    List<Diagnosis> findByAppointmentId(Long appointmentId);
    
    @Query("SELECT d FROM Diagnosis d WHERE d.patient.id = :patientId ORDER BY d.createdAt DESC")
    List<Diagnosis> findByPatientIdOrderByCreatedAtDesc(@Param("patientId") Long patientId);
    
    @Query("SELECT d FROM Diagnosis d WHERE d.doctor.id = :doctorId ORDER BY d.createdAt DESC")
    List<Diagnosis> findByDoctorIdOrderByCreatedAtDesc(@Param("doctorId") Long doctorId);
}
