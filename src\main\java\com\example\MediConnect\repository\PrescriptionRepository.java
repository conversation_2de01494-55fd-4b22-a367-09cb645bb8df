package com.example.MediConnect.repository;

import com.example.MediConnect.entity.Prescription;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PrescriptionRepository extends JpaRepository<Prescription, Long> {
    
    List<Prescription> findByDiagnosisId(Long diagnosisId);
    
    @Query("SELECT p FROM Prescription p WHERE p.diagnosis.patient.id = :patientId")
    List<Prescription> findByPatientId(@Param("patientId") Long patientId);
    
    @Query("SELECT p FROM Prescription p WHERE p.diagnosis.patient.id = :patientId AND p.isActive = :isActive")
    List<Prescription> findByPatientIdAndIsActive(@Param("patientId") Long patientId, @Param("isActive") Boolean isActive);
    
    @Query("SELECT p FROM Prescription p WHERE p.diagnosis.patient.id = :patientId ORDER BY p.createdAt DESC")
    List<Prescription> findByPatientIdOrderByCreatedAtDesc(@Param("patientId") Long patientId);
}
