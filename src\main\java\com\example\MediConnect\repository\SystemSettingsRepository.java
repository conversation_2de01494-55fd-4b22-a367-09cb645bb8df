package com.example.MediConnect.repository;

import com.example.MediConnect.entity.SystemSettings;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SystemSettingsRepository extends JpaRepository<SystemSettings, Long> {
    
    Optional<SystemSettings> findBySettingKey(String settingKey);
    
    List<SystemSettings> findByCategory(String category);
    
    List<SystemSettings> findByCategoryOrderBySettingKey(String category);
    
    @Query("SELECT s FROM SystemSettings s WHERE s.category IN :categories ORDER BY s.category, s.settingKey")
    List<SystemSettings> findByCategoriesOrderByCategoryAndSettingKey(@Param("categories") List<String> categories);
    
    @Query("SELECT s FROM SystemSettings s WHERE s.settingKey LIKE :keyPattern")
    List<SystemSettings> findBySettingKeyPattern(@Param("keyPattern") String keyPattern);
    
    boolean existsBySettingKey(String settingKey);
    
    void deleteBySettingKey(String settingKey);
    
    @Query("SELECT COUNT(s) FROM SystemSettings s WHERE s.category = :category")
    Long countByCategory(@Param("category") String category);
    
}
