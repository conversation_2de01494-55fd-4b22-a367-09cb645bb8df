package com.example.MediConnect.service;

import java.util.List;

import com.example.MediConnect.dto.ClinicDTO;
import com.example.MediConnect.dto.DoctorDTO;
import com.example.MediConnect.dto.UserDTO;
import com.example.MediConnect.dto.request.AddUserRequest;
import com.example.MediConnect.dto.response.SystemReportResponse;
import com.example.MediConnect.enums.UserStatus;

public interface AdminService {

    // Approval Management
    List<ClinicDTO> getPendingClinicApprovals();
    void approveClinic(Long clinicId);
    void rejectClinic(Long clinicId);

    List<DoctorDTO> getPendingDoctorApprovals();
    void approveDoctor(Long doctorId);
    void rejectDoctor(Long doctorId);

    // User Management
    List<UserDTO> getAllUsers();
    UserDTO getUserById(Long userId);
    UserDTO addUser(AddUserRequest request);
    void activateUser(Long userId);
    void deactivateUser(Long userId);
    List<UserDTO> getUsersByStatus(UserStatus status);
    void changeUserRole(Long userId, String newRole);

    // System Reports
    SystemReportResponse getSystemOverview();
    SystemReportResponse getUserStatistics();
    SystemReportResponse getAppointmentStatistics();

    // Sample Data Initialization
    void initializeSampleData();
}
