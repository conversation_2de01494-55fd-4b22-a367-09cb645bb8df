package com.example.MediConnect.service;

import com.example.MediConnect.dto.DiagnosisDTO;
import com.example.MediConnect.dto.request.DiagnosisRequest;

import java.util.List;

public interface DiagnosisService {
    
    DiagnosisDTO createDiagnosis(DiagnosisRequest diagnosisRequest);
    
    DiagnosisDTO getDiagnosisById(Long diagnosisId);
    
    List<DiagnosisDTO> getDiagnosesByPatient(Long patientId);
    
    List<DiagnosisDTO> getDiagnosesByDoctor(Long doctorId);
    
    DiagnosisDTO updateDiagnosis(Long diagnosisId, DiagnosisRequest diagnosisRequest);
    
    void deleteDiagnosis(Long diagnosisId);
}
