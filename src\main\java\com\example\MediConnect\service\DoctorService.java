package com.example.MediConnect.service;

import com.example.MediConnect.dto.DoctorDTO;
import com.example.MediConnect.dto.PatientDTO;
import com.example.MediConnect.dto.DiagnosisDTO;
import com.example.MediConnect.dto.FollowUpDTO;
import com.example.MediConnect.dto.request.DoctorRequest;
import com.example.MediConnect.dto.request.DiagnosisRequest;
import com.example.MediConnect.dto.request.FollowUpRequest;
import com.example.MediConnect.entity.Speciality;

import java.util.List;

public interface DoctorService {
    
    // Profile Management
    DoctorDTO getDoctorProfile(Long doctorId);
    DoctorDTO updateDoctorProfile(Long doctorId, DoctorRequest doctorRequest);
    
    // Patient Management
    List<PatientDTO> getAssignedPatients(Long doctorId);
    PatientDTO getPatientDetails(Long patientId);
    DiagnosisDTO logPatientDiagnosis(DiagnosisRequest diagnosisRequest);
    List<DiagnosisDTO> getPatientMedicalHistory(Long patientId);
    
    // Follow-up Management
    FollowUpDTO scheduleFollowUp(FollowUpRequest followUpRequest);
    List<FollowUpDTO> getScheduledFollowUps(Long doctorId);
    FollowUpDTO updateFollowUp(Long followUpId, FollowUpRequest followUpRequest);
    void cancelFollowUp(Long followUpId);
    
    // Search and Filter
    List<DoctorDTO> getDoctorsBySpecialty(Speciality speciality);
    List<DoctorDTO> getDoctorsByClinic(Long clinicId);
}
