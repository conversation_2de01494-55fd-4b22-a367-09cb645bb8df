package com.example.MediConnect.service;

import com.example.MediConnect.dto.DoctorDTO;
import com.example.MediConnect.dto.PatientDTO;
import com.example.MediConnect.dto.DiagnosisDTO;
import com.example.MediConnect.dto.FollowUpDTO;
import com.example.MediConnect.dto.AppointmentDTO;
import com.example.MediConnect.dto.request.DoctorRequest;
import com.example.MediConnect.dto.request.DiagnosisRequest;
import com.example.MediConnect.dto.request.FollowUpRequest;
import com.example.MediConnect.dto.request.AppointmentRequest;
import com.example.MediConnect.entity.Speciality;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface DoctorService {
    
    // Profile Management
    DoctorDTO getDoctorProfile(Long doctorId);
    DoctorDTO updateDoctorProfile(Long doctorId, DoctorRequest doctorRequest);
    
    // Patient Management
    List<PatientDTO> getAssignedPatients(Long doctorId);
    PatientDTO getPatientDetails(Long patientId);
    DiagnosisDTO logPatientDiagnosis(DiagnosisRequest diagnosisRequest);
    List<DiagnosisDTO> getPatientMedicalHistory(Long patientId);

    // Patient Statistics
    Map<String, Object> getDoctorPatientStatistics(Long doctorId);

    // Appointment Management with Real Database
    List<AppointmentDTO> getTodayAppointments(Long doctorId);
    List<AppointmentDTO> getUpcomingAppointments(Long doctorId);
    List<AppointmentDTO> getCompletedAppointments(Long doctorId);
    AppointmentDTO getAppointmentDetails(Long doctorId, Long appointmentId);
    AppointmentDTO rescheduleAppointment(Long doctorId, Long appointmentId, LocalDateTime newDateTime, String reason);
    AppointmentDTO updateAppointmentNotes(Long doctorId, Long appointmentId, String notes, String diagnosis);
    AppointmentDTO scheduleAppointment(Long doctorId, Long patientId, AppointmentRequest appointmentRequest);
    
    // Follow-up Management
    FollowUpDTO scheduleFollowUp(FollowUpRequest followUpRequest);
    List<FollowUpDTO> getScheduledFollowUps(Long doctorId);
    FollowUpDTO updateFollowUp(Long followUpId, FollowUpRequest followUpRequest);
    void cancelFollowUp(Long followUpId);
    
    // Search and Filter
    List<DoctorDTO> getDoctorsBySpecialty(Speciality speciality);
    List<DoctorDTO> getDoctorsByClinic(Long clinicId);
}
