package com.example.MediConnect.service;

import com.example.MediConnect.dto.FollowUpDTO;
import com.example.MediConnect.dto.request.FollowUpRequest;
import com.example.MediConnect.enums.FollowUpStatus;

import java.util.List;

public interface FollowUpService {
    
    FollowUpDTO createFollowUp(FollowUpRequest followUpRequest);
    
    FollowUpDTO getFollowUpById(Long followUpId);
    
    List<FollowUpDTO> getFollowUpsByPatient(Long patientId);
    
    List<FollowUpDTO> getFollowUpsByDoctor(Long doctorId);
    
    List<FollowUpDTO> getFollowUpsByStatus(FollowUpStatus status);
    
    FollowUpDTO updateFollowUp(Long followUpId, FollowUpRequest followUpRequest);
    
    FollowUpDTO updateFollowUpStatus(Long followUpId, FollowUpStatus status);
    
    void cancelFollowUp(Long followUpId);
}
