package com.example.MediConnect.service;

import com.example.MediConnect.entity.Appointment;
import com.example.MediConnect.entity.User;

public interface NotificationService {
    
    void sendAppointmentConfirmation(Appointment appointment);
    
    void sendAppointmentReminder(Appointment appointment);
    
    void sendAppointmentCancellation(Appointment appointment);
    
    void sendApprovalNotification(User user, boolean approved);
    
    void sendWelcomeEmail(User user);
    
    void sendPasswordResetEmail(User user, String resetToken);
}
