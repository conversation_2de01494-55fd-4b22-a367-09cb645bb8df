package com.example.MediConnect.service;

import java.time.LocalDate;
import java.util.List;

import com.example.MediConnect.dto.AppointmentDTO;
import com.example.MediConnect.dto.ClinicDTO;
import com.example.MediConnect.dto.DiagnosisDTO;
import com.example.MediConnect.dto.DoctorDTO;
import com.example.MediConnect.dto.PatientDTO;
import com.example.MediConnect.dto.PrescriptionDTO;
import com.example.MediConnect.dto.request.AppointmentRequest;
import com.example.MediConnect.enums.Specialization;

public interface PatientService {

    // Profile Management
    PatientDTO getPatientProfile(Long patientId);
    PatientDTO updatePatientProfile(Long patientId, PatientDTO patientDTO);

    // Appointment Booking
    AppointmentDTO bookAppointment(AppointmentRequest appointmentRequest);
    List<AppointmentDTO> getPatientAppointments(Long patientId);
    List<AppointmentDTO> getAppointmentHistory(Long patientId);
    void cancelAppointment(Long appointmentId);

    // Search Functionality
    List<DoctorDTO> searchDoctorsBySpecialty(Specialization specialization);
    List<ClinicDTO> searchClinicsBySpecialty(Specialization specialization);
    List<String> getDoctorAvailability(Long doctorId, LocalDate date);

    // Medical History
    List<DiagnosisDTO> getMedicalHistory(Long patientId);
    List<DiagnosisDTO> getDiagnoses(Long patientId);
    List<PrescriptionDTO> getPrescriptions(Long patientId);
}
