package com.example.MediConnect.service;

import com.example.MediConnect.dto.UserDTO;
import com.example.MediConnect.dto.response.SystemStatsResponse;
import com.example.MediConnect.controller.ReportsController;
import com.lowagie.text.*;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.awt.Color;
import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class SimplePdfReportService {

    @Autowired
    private AdminService adminService;

    @Autowired
    private ReportsController reportsController;

    public byte[] generateSystemOverviewReport() {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            Document document = new Document();
            PdfWriter.getInstance(document, baos);
            document.open();

            // Header
            addHeader(document, "MediConnect System Overview Report");

            // Get real system statistics
            SystemStatsResponse systemStats = reportsController.getSystemStats().getBody();

            // System Statistics with real data
            addRealSystemStatistics(document, systemStats);

            // User Growth Metrics
            addUserGrowthMetrics(document, systemStats);

            // Performance Metrics
            addPerformanceMetrics(document, systemStats);

            // User List
            List<UserDTO> users = adminService.getAllUsers();
            addUserList(document, users);

            document.close();
            return baos.toByteArray();
        } catch (Exception e) {
            throw new RuntimeException("Error generating PDF report: " + e.getMessage(), e);
        }
    }

    public byte[] generateUserReport() {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            Document document = new Document();
            PdfWriter.getInstance(document, baos);
            document.open();

            // Header
            addHeader(document, "MediConnect Users Report");

            // Get all users
            List<UserDTO> users = adminService.getAllUsers();

            // User Statistics
            addUserStatistics(document, users);

            // All Users Table
            addUserList(document, users);

            document.close();
            return baos.toByteArray();
        } catch (Exception e) {
            throw new RuntimeException("Error generating user PDF report: " + e.getMessage(), e);
        }
    }

    public byte[] generateAppointmentReport() {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            Document document = new Document();
            PdfWriter.getInstance(document, baos);
            document.open();

            // Header
            addHeader(document, "MediConnect Appointments Report");

            // Get all users for basic statistics
            List<UserDTO> users = adminService.getAllUsers();

            // Basic Statistics
            addBasicStatistics(document, users);

            document.close();
            return baos.toByteArray();
        } catch (Exception e) {
            throw new RuntimeException("Error generating appointment PDF report: " + e.getMessage(), e);
        }
    }

    private void addHeader(Document document, String title) throws DocumentException {
        // Title
        Font titleFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 20);
        Paragraph titleParagraph = new Paragraph(title, titleFont);
        titleParagraph.setAlignment(Element.ALIGN_CENTER);
        titleParagraph.setSpacingAfter(10);
        document.add(titleParagraph);

        // Subtitle with date
        String currentDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Font dateFont = FontFactory.getFont(FontFactory.HELVETICA, 12);
        Paragraph dateParagraph = new Paragraph("Generated on: " + currentDate, dateFont);
        dateParagraph.setAlignment(Element.ALIGN_CENTER);
        dateParagraph.setSpacingAfter(20);
        document.add(dateParagraph);
    }

    private void addSystemStatistics(Document document, List<UserDTO> users) throws DocumentException {
        // Section Title
        Font sectionFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 16);
        Paragraph sectionTitle = new Paragraph("System Statistics", sectionFont);
        sectionTitle.setSpacingBefore(20);
        sectionTitle.setSpacingAfter(10);
        document.add(sectionTitle);

        // Calculate statistics
        long totalUsers = users.size();
        long activeUsers = users.stream().filter(u -> "ACTIVE".equals(u.getStatus().toString())).count();
        long totalDoctors = users.stream().filter(u -> "DOCTOR".equals(u.getRole().toString())).count();
        long totalPatients = users.stream().filter(u -> "PATIENT".equals(u.getRole().toString())).count();
        long totalClinics = users.stream().filter(u -> "CLINIC".equals(u.getRole().toString())).count();

        // Statistics Table
        PdfPTable statsTable = new PdfPTable(2);
        statsTable.setWidthPercentage(100);
        statsTable.setSpacingAfter(20);

        // Header
        addTableHeader(statsTable, "Metric", "Count");

        // Data rows
        addTableRow(statsTable, "Total Users", String.valueOf(totalUsers));
        addTableRow(statsTable, "Active Users", String.valueOf(activeUsers));
        addTableRow(statsTable, "Total Doctors", String.valueOf(totalDoctors));
        addTableRow(statsTable, "Total Patients", String.valueOf(totalPatients));
        addTableRow(statsTable, "Total Clinics", String.valueOf(totalClinics));

        document.add(statsTable);
    }

    private void addUserStatistics(Document document, List<UserDTO> users) throws DocumentException {
        // Section Title
        Font sectionFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 16);
        Paragraph sectionTitle = new Paragraph("User Statistics by Role", sectionFont);
        sectionTitle.setSpacingBefore(20);
        sectionTitle.setSpacingAfter(10);
        document.add(sectionTitle);

        // Count users by role
        long adminCount = users.stream().filter(u -> "ADMIN".equals(u.getRole().toString())).count();
        long doctorCount = users.stream().filter(u -> "DOCTOR".equals(u.getRole().toString())).count();
        long patientCount = users.stream().filter(u -> "PATIENT".equals(u.getRole().toString())).count();
        long clinicCount = users.stream().filter(u -> "CLINIC".equals(u.getRole().toString())).count();
        long staffCount = users.stream().filter(u -> "CLINIC_STAFF".equals(u.getRole().toString())).count();

        // Role Statistics Table
        PdfPTable roleTable = new PdfPTable(3);
        roleTable.setWidthPercentage(100);
        roleTable.setSpacingAfter(20);

        // Header
        addTableHeader(roleTable, "Role", "Count", "Percentage");

        // Calculate total for percentages
        long total = users.size();

        // Data rows
        addRoleRow(roleTable, "Administrators", adminCount, total);
        addRoleRow(roleTable, "Doctors", doctorCount, total);
        addRoleRow(roleTable, "Patients", patientCount, total);
        addRoleRow(roleTable, "Clinics", clinicCount, total);
        addRoleRow(roleTable, "Clinic Staff", staffCount, total);

        document.add(roleTable);
    }

    private void addUserList(Document document, List<UserDTO> users) throws DocumentException {
        // Section Title
        Font sectionFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 16);
        Paragraph sectionTitle = new Paragraph("All Users", sectionFont);
        sectionTitle.setSpacingBefore(20);
        sectionTitle.setSpacingAfter(10);
        document.add(sectionTitle);

        // Users Table
        PdfPTable usersTable = new PdfPTable(5);
        usersTable.setWidthPercentage(100);
        usersTable.setSpacingAfter(20);

        // Header
        addTableHeader(usersTable, "Name", "Email", "Role", "Status", "Created");

        // Data rows
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (UserDTO user : users) {
            addTableRow(usersTable,
                user.getName(),
                user.getEmail(),
                user.getRole().toString(),
                user.getStatus().toString(),
                user.getCreatedAt().format(formatter)
            );
        }

        document.add(usersTable);
    }

    private void addBasicStatistics(Document document, List<UserDTO> users) throws DocumentException {
        // Section Title
        Font sectionFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 16);
        Paragraph sectionTitle = new Paragraph("Basic Statistics", sectionFont);
        sectionTitle.setSpacingBefore(20);
        sectionTitle.setSpacingAfter(10);
        document.add(sectionTitle);

        // Basic Statistics Table
        PdfPTable statsTable = new PdfPTable(2);
        statsTable.setWidthPercentage(100);
        statsTable.setSpacingAfter(20);

        // Header
        addTableHeader(statsTable, "Metric", "Count");

        // Data rows
        addTableRow(statsTable, "Total Users", String.valueOf(users.size()));
        addTableRow(statsTable, "Total Doctors", String.valueOf(users.stream().filter(u -> "DOCTOR".equals(u.getRole().toString())).count()));
        addTableRow(statsTable, "Total Patients", String.valueOf(users.stream().filter(u -> "PATIENT".equals(u.getRole().toString())).count()));

        document.add(statsTable);
    }

    private void addRoleRow(PdfPTable table, String role, long count, long total) {
        double percentage = total > 0 ? (count * 100.0 / total) : 0;
        addTableRow(table, role, String.valueOf(count), String.format("%.1f%%", percentage));
    }

    private void addTableHeader(PdfPTable table, String... headers) {
        Font headerFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 12);
        for (String header : headers) {
            PdfPCell cell = new PdfPCell(new Phrase(header, headerFont));
            cell.setBackgroundColor(Color.LIGHT_GRAY);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setPadding(8);
            table.addCell(cell);
        }
    }

    private void addTableRow(PdfPTable table, String... values) {
        Font dataFont = FontFactory.getFont(FontFactory.HELVETICA, 10);
        for (String value : values) {
            PdfPCell cell = new PdfPCell(new Phrase(value != null ? value : "", dataFont));
            cell.setPadding(5);
            table.addCell(cell);
        }
    }

    private void addRealSystemStatistics(Document document, SystemStatsResponse systemStats) throws DocumentException {
        // Section Title
        Font sectionFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 16);
        Paragraph sectionTitle = new Paragraph("System Statistics (Real-Time Data)", sectionFont);
        sectionTitle.setSpacingBefore(20);
        sectionTitle.setSpacingAfter(10);
        document.add(sectionTitle);

        // Statistics Table
        PdfPTable statsTable = new PdfPTable(2);
        statsTable.setWidthPercentage(100);
        statsTable.setSpacingAfter(20);

        // Header
        addTableHeader(statsTable, "Metric", "Value");

        // User Statistics
        addTableRow(statsTable, "Total Users", String.valueOf(systemStats.getTotalUsers()));
        addTableRow(statsTable, "Active Users", String.valueOf(systemStats.getActiveUsers()));
        addTableRow(statsTable, "Pending Users", String.valueOf(systemStats.getPendingUsers()));
        addTableRow(statsTable, "Inactive Users", String.valueOf(systemStats.getInactiveUsers()));
        addTableRow(statsTable, "Suspended Users", String.valueOf(systemStats.getSuspendedUsers()));

        // Role Distribution
        addTableRow(statsTable, "Doctors", String.valueOf(systemStats.getDoctorCount()));
        addTableRow(statsTable, "Patients", String.valueOf(systemStats.getPatientCount()));
        addTableRow(statsTable, "Clinics", String.valueOf(systemStats.getClinicCount()));
        addTableRow(statsTable, "Clinic Staff", String.valueOf(systemStats.getClinicStaffCount()));
        addTableRow(statsTable, "Administrators", String.valueOf(systemStats.getAdminCount()));

        document.add(statsTable);
    }

    private void addUserGrowthMetrics(Document document, SystemStatsResponse systemStats) throws DocumentException {
        // Section Title
        Font sectionFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 16);
        Paragraph sectionTitle = new Paragraph("User Growth Metrics", sectionFont);
        sectionTitle.setSpacingBefore(20);
        sectionTitle.setSpacingAfter(10);
        document.add(sectionTitle);

        // Growth Table
        PdfPTable growthTable = new PdfPTable(2);
        growthTable.setWidthPercentage(100);
        growthTable.setSpacingAfter(20);

        // Header
        addTableHeader(growthTable, "Metric", "Value");

        // Growth Data
        addTableRow(growthTable, "New Users This Month", String.valueOf(systemStats.getNewUsersThisMonth()));
        addTableRow(growthTable, "New Users Last Month", String.valueOf(systemStats.getNewUsersLastMonth()));
        addTableRow(growthTable, "Growth Rate", String.format("%.2f%%", systemStats.getUserGrowthRate()));

        document.add(growthTable);
    }

    private void addPerformanceMetrics(Document document, SystemStatsResponse systemStats) throws DocumentException {
        // Section Title
        Font sectionFont = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 16);
        Paragraph sectionTitle = new Paragraph("System Performance", sectionFont);
        sectionTitle.setSpacingBefore(20);
        sectionTitle.setSpacingAfter(10);
        document.add(sectionTitle);

        // Performance Table
        PdfPTable perfTable = new PdfPTable(2);
        perfTable.setWidthPercentage(100);
        perfTable.setSpacingAfter(20);

        // Header
        addTableHeader(perfTable, "Metric", "Value");

        // Performance Data
        addTableRow(perfTable, "System Uptime", systemStats.getSystemUptime());
        addTableRow(perfTable, "API Response Time", systemStats.getApiResponseTime());
        addTableRow(perfTable, "Active Sessions", String.valueOf(systemStats.getActiveSessions()));
        addTableRow(perfTable, "Database Status", systemStats.getDatabaseStatus());
        addTableRow(perfTable, "Last Backup", systemStats.getLastBackup());
        addTableRow(perfTable, "Storage Used", systemStats.getStorageUsed() + " MB");

        document.add(perfTable);
    }
}
