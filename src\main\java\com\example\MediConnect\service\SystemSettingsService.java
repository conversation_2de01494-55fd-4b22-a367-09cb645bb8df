package com.example.MediConnect.service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.example.MediConnect.dto.request.SecuritySettingsRequest;
import com.example.MediConnect.dto.response.SecuritySettingsResponse;
import com.example.MediConnect.entity.SystemSettings;
import com.example.MediConnect.repository.SystemSettingsRepository;

@Service
@Transactional
public class SystemSettingsService {

    @Autowired
    private SystemSettingsRepository systemSettingsRepository;

    // Setting keys constants
    private static final String TWO_FACTOR_AUTH_ENABLED = "security.two_factor_auth_enabled";
    private static final String STRONG_PASSWORD_POLICY_ENABLED = "security.strong_password_policy_enabled";
    private static final String SESSION_TIMEOUT_MINUTES = "security.session_timeout_minutes";
    private static final String MAX_LOGIN_ATTEMPTS = "security.max_login_attempts";
    private static final String DATA_ENCRYPTION_ENABLED = "security.data_encryption_enabled";
    private static final String AUDIT_LOGGING_ENABLED = "security.audit_logging_enabled";
    private static final String PASSWORD_EXPIRATION_ENABLED = "security.password_expiration_enabled";
    private static final String PASSWORD_EXPIRATION_DAYS = "security.password_expiration_days";
    private static final String ACCOUNT_LOCKOUT_ENABLED = "security.account_lockout_enabled";
    private static final String LOCKOUT_DURATION_MINUTES = "security.lockout_duration_minutes";
    private static final String IP_WHITELIST_ENABLED = "security.ip_whitelist_enabled";
    private static final String ALLOWED_IP_ADDRESSES = "security.allowed_ip_addresses";
    private static final String EMAIL_NOTIFICATIONS_ENABLED = "notifications.email_enabled";
    private static final String SECURITY_ALERTS_ENABLED = "notifications.security_alerts_enabled";
    private static final String LOGIN_NOTIFICATIONS_ENABLED = "notifications.login_notifications_enabled";
    private static final String MAINTENANCE_MODE_ENABLED = "system.maintenance_mode_enabled";
    private static final String MAINTENANCE_MESSAGE = "system.maintenance_message";

    public SecuritySettingsResponse getSecuritySettings() {
        SecuritySettingsResponse response = new SecuritySettingsResponse();

        // Authentication Settings
        response.setTwoFactorAuthEnabled(getBooleanSetting(TWO_FACTOR_AUTH_ENABLED, false));
        response.setStrongPasswordPolicyEnabled(getBooleanSetting(STRONG_PASSWORD_POLICY_ENABLED, true));
        response.setSessionTimeoutMinutes(getIntegerSetting(SESSION_TIMEOUT_MINUTES, 30));
        response.setMaxLoginAttempts(getIntegerSetting(MAX_LOGIN_ATTEMPTS, 5));

        // Data Security Settings
        response.setDataEncryptionEnabled(getBooleanSetting(DATA_ENCRYPTION_ENABLED, true));
        response.setAuditLoggingEnabled(getBooleanSetting(AUDIT_LOGGING_ENABLED, true));

        // Additional security settings
        response.setPasswordExpirationEnabled(getBooleanSetting(PASSWORD_EXPIRATION_ENABLED, false));
        response.setPasswordExpirationDays(getIntegerSetting(PASSWORD_EXPIRATION_DAYS, 90));
        response.setAccountLockoutEnabled(getBooleanSetting(ACCOUNT_LOCKOUT_ENABLED, true));
        response.setLockoutDurationMinutes(getIntegerSetting(LOCKOUT_DURATION_MINUTES, 15));
        response.setIpWhitelistEnabled(getBooleanSetting(IP_WHITELIST_ENABLED, false));
        response.setAllowedIpAddresses(getStringSetting(ALLOWED_IP_ADDRESSES, ""));

        // Email notification settings
        response.setEmailNotificationsEnabled(getBooleanSetting(EMAIL_NOTIFICATIONS_ENABLED, true));
        response.setSecurityAlertsEnabled(getBooleanSetting(SECURITY_ALERTS_ENABLED, true));
        response.setLoginNotificationsEnabled(getBooleanSetting(LOGIN_NOTIFICATIONS_ENABLED, false));

        // System maintenance settings
        response.setMaintenanceModeEnabled(getBooleanSetting(MAINTENANCE_MODE_ENABLED, false));
        response.setMaintenanceMessage(getStringSetting(MAINTENANCE_MESSAGE, "System is under maintenance. Please try again later."));

        // Calculate security metrics
        calculateSecurityMetrics(response);

        return response;
    }

    public SecuritySettingsResponse saveSecuritySettings(SecuritySettingsRequest request, String updatedBy) {
        // Save authentication settings
        saveSetting(TWO_FACTOR_AUTH_ENABLED, request.getTwoFactorAuthEnabled().toString(), "BOOLEAN", "SECURITY", "Enable two-factor authentication", updatedBy);
        saveSetting(STRONG_PASSWORD_POLICY_ENABLED, request.getStrongPasswordPolicyEnabled().toString(), "BOOLEAN", "SECURITY", "Enable strong password policy", updatedBy);
        saveSetting(SESSION_TIMEOUT_MINUTES, request.getSessionTimeoutMinutes().toString(), "INTEGER", "SECURITY", "Session timeout in minutes", updatedBy);
        saveSetting(MAX_LOGIN_ATTEMPTS, request.getMaxLoginAttempts().toString(), "INTEGER", "SECURITY", "Maximum login attempts before lockout", updatedBy);

        // Save data security settings
        saveSetting(DATA_ENCRYPTION_ENABLED, request.getDataEncryptionEnabled().toString(), "BOOLEAN", "SECURITY", "Enable data encryption at rest", updatedBy);
        saveSetting(AUDIT_LOGGING_ENABLED, request.getAuditLoggingEnabled().toString(), "BOOLEAN", "SECURITY", "Enable audit logging", updatedBy);

        // Save additional settings if provided
        if (request.getPasswordExpirationEnabled() != null) {
            saveSetting(PASSWORD_EXPIRATION_ENABLED, request.getPasswordExpirationEnabled().toString(), "BOOLEAN", "SECURITY", "Enable password expiration", updatedBy);
        }
        if (request.getPasswordExpirationDays() != null) {
            saveSetting(PASSWORD_EXPIRATION_DAYS, request.getPasswordExpirationDays().toString(), "INTEGER", "SECURITY", "Password expiration days", updatedBy);
        }
        if (request.getAccountLockoutEnabled() != null) {
            saveSetting(ACCOUNT_LOCKOUT_ENABLED, request.getAccountLockoutEnabled().toString(), "BOOLEAN", "SECURITY", "Enable account lockout", updatedBy);
        }
        if (request.getLockoutDurationMinutes() != null) {
            saveSetting(LOCKOUT_DURATION_MINUTES, request.getLockoutDurationMinutes().toString(), "INTEGER", "SECURITY", "Account lockout duration in minutes", updatedBy);
        }
        if (request.getIpWhitelistEnabled() != null) {
            saveSetting(IP_WHITELIST_ENABLED, request.getIpWhitelistEnabled().toString(), "BOOLEAN", "SECURITY", "Enable IP whitelist", updatedBy);
        }
        if (request.getAllowedIpAddresses() != null) {
            saveSetting(ALLOWED_IP_ADDRESSES, request.getAllowedIpAddresses(), "STRING", "SECURITY", "Allowed IP addresses (comma-separated)", updatedBy);
        }
        if (request.getEmailNotificationsEnabled() != null) {
            saveSetting(EMAIL_NOTIFICATIONS_ENABLED, request.getEmailNotificationsEnabled().toString(), "BOOLEAN", "NOTIFICATIONS", "Enable email notifications", updatedBy);
        }
        if (request.getSecurityAlertsEnabled() != null) {
            saveSetting(SECURITY_ALERTS_ENABLED, request.getSecurityAlertsEnabled().toString(), "BOOLEAN", "NOTIFICATIONS", "Enable security alerts", updatedBy);
        }
        if (request.getLoginNotificationsEnabled() != null) {
            saveSetting(LOGIN_NOTIFICATIONS_ENABLED, request.getLoginNotificationsEnabled().toString(), "BOOLEAN", "NOTIFICATIONS", "Enable login notifications", updatedBy);
        }
        if (request.getMaintenanceModeEnabled() != null) {
            saveSetting(MAINTENANCE_MODE_ENABLED, request.getMaintenanceModeEnabled().toString(), "BOOLEAN", "SYSTEM", "Enable maintenance mode", updatedBy);
        }
        if (request.getMaintenanceMessage() != null) {
            saveSetting(MAINTENANCE_MESSAGE, request.getMaintenanceMessage(), "STRING", "SYSTEM", "Maintenance mode message", updatedBy);
        }

        // Return updated settings
        return getSecuritySettings();
    }

    private void saveSetting(String key, String value, String type, String category, String description, String updatedBy) {
        Optional<SystemSettings> existingSetting = systemSettingsRepository.findBySettingKey(key);

        SystemSettings setting;
        if (existingSetting.isPresent()) {
            setting = existingSetting.get();
            setting.setSettingValue(value);
            setting.setUpdatedBy(updatedBy);
        } else {
            setting = new SystemSettings(key, value, type, category, description);
            setting.setUpdatedBy(updatedBy);
        }

        systemSettingsRepository.save(setting);
    }

    private Boolean getBooleanSetting(String key, Boolean defaultValue) {
        Optional<SystemSettings> setting = systemSettingsRepository.findBySettingKey(key);
        if (setting.isPresent()) {
            return Boolean.parseBoolean(setting.get().getSettingValue());
        }
        return defaultValue;
    }

    private Integer getIntegerSetting(String key, Integer defaultValue) {
        Optional<SystemSettings> setting = systemSettingsRepository.findBySettingKey(key);
        if (setting.isPresent()) {
            try {
                return Integer.parseInt(setting.get().getSettingValue());
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    private String getStringSetting(String key, String defaultValue) {
        Optional<SystemSettings> setting = systemSettingsRepository.findBySettingKey(key);
        if (setting.isPresent()) {
            return setting.get().getSettingValue();
        }
        return defaultValue;
    }

    private void calculateSecurityMetrics(SecuritySettingsResponse response) {
        int score = 0;
        List<String> recommendations = new ArrayList<>();

        // Calculate security score based on enabled features
        if (response.getTwoFactorAuthEnabled()) score += 20;
        else recommendations.add("Enable two-factor authentication for enhanced security");

        if (response.getStrongPasswordPolicyEnabled()) score += 15;
        else recommendations.add("Enable strong password policy");

        if (response.getDataEncryptionEnabled()) score += 20;
        else recommendations.add("Enable data encryption at rest");

        if (response.getAuditLoggingEnabled()) score += 15;
        else recommendations.add("Enable audit logging for compliance");

        if (response.getAccountLockoutEnabled()) score += 10;
        else recommendations.add("Enable account lockout protection");

        if (response.getSessionTimeoutMinutes() <= 30) score += 10;
        else recommendations.add("Consider reducing session timeout to 30 minutes or less");

        if (response.getMaxLoginAttempts() <= 5) score += 10;
        else recommendations.add("Consider reducing max login attempts to 5 or less");

        // Determine security level
        String securityLevel;
        if (score >= 80) securityLevel = "HIGH";
        else if (score >= 60) securityLevel = "MEDIUM";
        else securityLevel = "LOW";

        response.setSecurityScore(score);
        response.setSecurityLevel(securityLevel);
        response.setHasSecurityIssues(score < 70);
        response.setSecurityRecommendations(recommendations.toArray(new String[0]));
        response.setLastUpdated(LocalDateTime.now());
        response.setVersion("1.0");
    }

    public void initializeDefaultSettings() {
        // Initialize default settings if they don't exist
        if (!systemSettingsRepository.existsBySettingKey(TWO_FACTOR_AUTH_ENABLED)) {
            saveSetting(TWO_FACTOR_AUTH_ENABLED, "false", "BOOLEAN", "SECURITY", "Enable two-factor authentication", "SYSTEM");
        }
        if (!systemSettingsRepository.existsBySettingKey(STRONG_PASSWORD_POLICY_ENABLED)) {
            saveSetting(STRONG_PASSWORD_POLICY_ENABLED, "true", "BOOLEAN", "SECURITY", "Enable strong password policy", "SYSTEM");
        }
        if (!systemSettingsRepository.existsBySettingKey(SESSION_TIMEOUT_MINUTES)) {
            saveSetting(SESSION_TIMEOUT_MINUTES, "30", "INTEGER", "SECURITY", "Session timeout in minutes", "SYSTEM");
        }
        if (!systemSettingsRepository.existsBySettingKey(MAX_LOGIN_ATTEMPTS)) {
            saveSetting(MAX_LOGIN_ATTEMPTS, "5", "INTEGER", "SECURITY", "Maximum login attempts before lockout", "SYSTEM");
        }
        if (!systemSettingsRepository.existsBySettingKey(DATA_ENCRYPTION_ENABLED)) {
            saveSetting(DATA_ENCRYPTION_ENABLED, "true", "BOOLEAN", "SECURITY", "Enable data encryption at rest", "SYSTEM");
        }
        if (!systemSettingsRepository.existsBySettingKey(AUDIT_LOGGING_ENABLED)) {
            saveSetting(AUDIT_LOGGING_ENABLED, "true", "BOOLEAN", "SECURITY", "Enable audit logging", "SYSTEM");
        }
    }
}
