package com.example.MediConnect.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.example.MediConnect.dto.ClinicDTO;
import com.example.MediConnect.dto.DoctorDTO;
import com.example.MediConnect.dto.UserDTO;
import com.example.MediConnect.dto.request.AddUserRequest;
import com.example.MediConnect.dto.response.SystemReportResponse;
import com.example.MediConnect.entity.Admin;
import com.example.MediConnect.entity.Clinic;
import com.example.MediConnect.entity.ClinicStaff;
import com.example.MediConnect.entity.Doctor;
import com.example.MediConnect.entity.Patient;
import com.example.MediConnect.entity.User;
import com.example.MediConnect.enums.AppointmentStatus;
import com.example.MediConnect.enums.ClinicStatus;
import com.example.MediConnect.enums.DoctorStatus;
import com.example.MediConnect.enums.Role;
import com.example.MediConnect.enums.UserStatus;
import com.example.MediConnect.exception.ResourceNotFoundException;
import com.example.MediConnect.repository.AppointmentRepository;
import com.example.MediConnect.repository.ClinicRepository;
import com.example.MediConnect.repository.DoctorRepository;
import com.example.MediConnect.repository.PatientRepository;
import com.example.MediConnect.repository.UserRepository;
import com.example.MediConnect.service.AdminService;
import com.example.MediConnect.service.DataInitializationService;
import com.example.MediConnect.util.Constants;
import org.springframework.security.crypto.password.PasswordEncoder;

@Service
@Transactional
public class AdminServiceImpl implements AdminService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ClinicRepository clinicRepository;

    @Autowired
    private DoctorRepository doctorRepository;

    @Autowired
    private PatientRepository patientRepository;

    @Autowired
    private AppointmentRepository appointmentRepository;

    @Autowired
    private DataInitializationService dataInitializationService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public List<ClinicDTO> getPendingClinicApprovals() {
        List<Clinic> pendingClinics = clinicRepository.findByClinicStatus(ClinicStatus.PENDING_APPROVAL);
        return pendingClinics.stream()
                .map(this::convertToClinicDTO)
                .collect(Collectors.toList());
    }

    @Override
    public void approveClinic(Long clinicId) {
        Clinic clinic = clinicRepository.findById(clinicId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.CLINIC_NOT_FOUND));

        clinic.setClinicStatus(ClinicStatus.ACTIVE);
        clinic.setStatus(UserStatus.ACTIVE);
        clinicRepository.save(clinic);
    }

    @Override
    public void rejectClinic(Long clinicId) {
        Clinic clinic = clinicRepository.findById(clinicId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.CLINIC_NOT_FOUND));

        clinic.setClinicStatus(ClinicStatus.REJECTED);
        clinic.setStatus(UserStatus.REJECTED);
        clinicRepository.save(clinic);
    }

    @Override
    public List<DoctorDTO> getPendingDoctorApprovals() {
        List<Doctor> pendingDoctors = doctorRepository.findByDoctorStatus(DoctorStatus.PENDING_APPROVAL);
        return pendingDoctors.stream()
                .map(this::convertToDoctorDTO)
                .collect(Collectors.toList());
    }

    @Override
    public void approveDoctor(Long doctorId) {
        Doctor doctor = doctorRepository.findById(doctorId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.DOCTOR_NOT_FOUND));

        doctor.setDoctorStatus(DoctorStatus.ACTIVE);
        doctor.setStatus(UserStatus.ACTIVE);
        doctorRepository.save(doctor);
    }

    @Override
    public void rejectDoctor(Long doctorId) {
        Doctor doctor = doctorRepository.findById(doctorId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.DOCTOR_NOT_FOUND));

        doctor.setDoctorStatus(DoctorStatus.REJECTED);
        doctor.setStatus(UserStatus.REJECTED);
        doctorRepository.save(doctor);
    }

    @Override
    public List<UserDTO> getAllUsers() {
        List<User> users = userRepository.findAll();
        return users.stream()
                .map(this::convertToUserDTO)
                .collect(Collectors.toList());
    }

    @Override
    public UserDTO getUserById(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.USER_NOT_FOUND));
        return convertToUserDTO(user);
    }

    @Override
    public UserDTO addUser(AddUserRequest request) {
        // Check if email already exists
        if (userRepository.findByEmail(request.getEmail()).isPresent()) {
            throw new IllegalArgumentException("Email already exists: " + request.getEmail());
        }

        // Validate role
        if (request.getRole() == null) {
            throw new IllegalArgumentException("User role is required");
        }

        // Create user based on role
        User createdUser = createUserByRole(request);

        // Convert to DTO and return
        return convertToUserDTO(createdUser);
    }

    @Override
    public void activateUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.USER_NOT_FOUND));
        user.setStatus(UserStatus.ACTIVE);
        userRepository.save(user);
    }

    @Override
    public void deactivateUser(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.USER_NOT_FOUND));
        user.setStatus(UserStatus.INACTIVE);
        userRepository.save(user);
    }

    @Override
    public List<UserDTO> getUsersByStatus(UserStatus status) {
        List<User> users = userRepository.findByStatus(status);
        return users.stream()
                .map(this::convertToUserDTO)
                .collect(Collectors.toList());
    }

    @Override
    public void changeUserRole(Long userId, String newRole) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.USER_NOT_FOUND));

        Role targetRole;
        try {
            targetRole = Role.valueOf(newRole.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid role: " + newRole + ". Valid roles are: ADMIN, DOCTOR, PATIENT, CLINIC, CLINIC_STAFF");
        }

        // Check if role change is needed
        if (user.getRole() == targetRole) {
            throw new IllegalArgumentException("User already has the role: " + targetRole);
        }

        // Handle role transition
        handleRoleTransition(user, targetRole);
    }

    @Override
    public SystemReportResponse getSystemOverview() {
        SystemReportResponse report = new SystemReportResponse();

        report.setTotalUsers(userRepository.count());
        report.setTotalDoctors(userRepository.countByRole(Role.DOCTOR));
        report.setTotalClinics(userRepository.countByRole(Role.CLINIC));
        report.setTotalPatients(userRepository.countByRole(Role.PATIENT));
        report.setTotalAppointments(appointmentRepository.count());

        report.setPendingDoctorApprovals(doctorRepository.countByStatus(DoctorStatus.PENDING_APPROVAL));
        report.setPendingClinicApprovals(clinicRepository.countByStatus(ClinicStatus.PENDING_APPROVAL));

        report.setCompletedAppointments(appointmentRepository.countByStatus(AppointmentStatus.COMPLETED));
        report.setCancelledAppointments(appointmentRepository.countByStatus(AppointmentStatus.CANCELLED));

        return report;
    }

    @Override
    public SystemReportResponse getUserStatistics() {
        SystemReportResponse report = new SystemReportResponse();

        report.setTotalUsers(userRepository.count());
        report.setTotalDoctors(userRepository.countByRole(Role.DOCTOR));
        report.setTotalClinics(userRepository.countByRole(Role.CLINIC));
        report.setTotalPatients(userRepository.countByRole(Role.PATIENT));

        return report;
    }

    @Override
    public SystemReportResponse getAppointmentStatistics() {
        SystemReportResponse report = new SystemReportResponse();

        report.setTotalAppointments(appointmentRepository.count());
        report.setCompletedAppointments(appointmentRepository.countByStatus(AppointmentStatus.COMPLETED));
        report.setCancelledAppointments(appointmentRepository.countByStatus(AppointmentStatus.CANCELLED));

        return report;
    }

    @Override
    public void initializeSampleData() {
        try {
            // Force sample data initialization by calling the service directly
            dataInitializationService.forceInitializeSampleData();
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize sample data", e);
        }
    }

    private ClinicDTO convertToClinicDTO(Clinic clinic) {
        ClinicDTO dto = new ClinicDTO();
        dto.setId(clinic.getId());
        dto.setName(clinic.getName());
        dto.setEmail(clinic.getEmail());
        dto.setPhoneNumber(clinic.getPhoneNumber());
        dto.setAddress(clinic.getAddress());
        dto.setClinicName(clinic.getClinicName());
        dto.setLicenseNumber(clinic.getLicenseNumber());
        dto.setDescription(clinic.getDescription());
        dto.setClinicStatus(clinic.getClinicStatus());
        dto.setOperatingHours(clinic.getOperatingHours());
        dto.setEmergencyContact(clinic.getEmergencyContact());
        dto.setWebsiteUrl(clinic.getWebsiteUrl());
        dto.setCreatedAt(clinic.getCreatedAt());
        dto.setUpdatedAt(clinic.getUpdatedAt());

        if (clinic.getDoctors() != null) {
            dto.setTotalDoctors(clinic.getDoctors().size());
        }
        if (clinic.getClinicStaff() != null) {
            dto.setTotalStaff(clinic.getClinicStaff().size());
        }

        return dto;
    }

    private DoctorDTO convertToDoctorDTO(Doctor doctor) {
        DoctorDTO dto = new DoctorDTO();
        dto.setId(doctor.getId());
        dto.setName(doctor.getName());
        dto.setEmail(doctor.getEmail());
        dto.setPhoneNumber(doctor.getPhoneNumber());
        dto.setAddress(doctor.getAddress());
        dto.setDateOfBirth(doctor.getDateOfBirth());
        dto.setGender(doctor.getGender());
        dto.setMedicalLicense(doctor.getMedicalLicense());
        dto.setYearsOfExperience(doctor.getYearsOfExperience());
        dto.setQualification(doctor.getQualification());
        dto.setDoctorStatus(doctor.getDoctorStatus());
        dto.setConsultationFee(doctor.getConsultationFee());
        dto.setBio(doctor.getBio());
        dto.setCreatedAt(doctor.getCreatedAt());
        dto.setUpdatedAt(doctor.getUpdatedAt());

        if (doctor.getSpeciality() != null) {
            dto.setSpecialtyName(doctor.getSpeciality().getName().toString());
        }

        if (doctor.getClinic() != null) {
            dto.setClinicId(doctor.getClinic().getId());
            dto.setClinicName(doctor.getClinic().getClinicName());
        }

        return dto;
    }

    private UserDTO convertToUserDTO(User user) {
        UserDTO dto = new UserDTO();
        dto.setId(user.getId());
        dto.setName(user.getName());
        dto.setEmail(user.getEmail());
        dto.setPhoneNumber(user.getPhoneNumber());
        dto.setAddress(user.getAddress());
        dto.setDateOfBirth(user.getDateOfBirth());
        dto.setGender(user.getGender());
        dto.setRole(user.getRole());
        dto.setStatus(user.getStatus());
        dto.setCreatedAt(user.getCreatedAt());
        dto.setUpdatedAt(user.getUpdatedAt());
        dto.setLastLogin(user.getLastLogin());

        // Add role-specific fields based on user type
        if (user instanceof Doctor) {
            Doctor doctor = (Doctor) user;
            dto.setMedicalLicense(doctor.getMedicalLicense());
            dto.setYearsOfExperience(doctor.getYearsOfExperience());
            dto.setQualification(doctor.getQualification());
            dto.setDoctorStatus(doctor.getDoctorStatus() != null ? doctor.getDoctorStatus().toString() : null);
            dto.setConsultationFee(doctor.getConsultationFee());
            dto.setBio(doctor.getBio());

            if (doctor.getSpeciality() != null) {
                dto.setSpecialtyName(doctor.getSpeciality().getName().toString());
            }

            if (doctor.getClinic() != null) {
                dto.setClinicId(doctor.getClinic().getId());
                dto.setClinicName(doctor.getClinic().getClinicName());
            }
        } else if (user instanceof Patient) {
            Patient patient = (Patient) user;
            dto.setPatientId(patient.getPatientId());
            dto.setEmergencyContactName(patient.getEmergencyContactName());
            dto.setEmergencyContactPhone(patient.getEmergencyContactPhone());
            dto.setBloodGroup(patient.getBloodGroup());
            dto.setAllergies(patient.getAllergies());
            dto.setMedicalHistory(patient.getMedicalHistory());
            dto.setInsuranceProvider(patient.getInsuranceProvider());
            dto.setInsuranceNumber(patient.getInsuranceNumber());
        } else if (user instanceof Clinic) {
            Clinic clinic = (Clinic) user;
            dto.setClinicLicenseNumber(clinic.getLicenseNumber());
            dto.setDescription(clinic.getDescription());
            dto.setClinicStatus(clinic.getClinicStatus() != null ? clinic.getClinicStatus().toString() : null);
            dto.setOperatingHours(clinic.getOperatingHours());
            dto.setEmergencyContact(clinic.getEmergencyContact());
            dto.setWebsiteUrl(clinic.getWebsiteUrl());
        } else if (user instanceof Admin) {
            Admin admin = (Admin) user;
            dto.setEmployeeId(admin.getEmployeeId());
            dto.setDepartment(admin.getDepartment());
            dto.setAccessLevel(admin.getAccessLevel());
        } else if (user instanceof ClinicStaff) {
            ClinicStaff staff = (ClinicStaff) user;
            dto.setEmployeeId(staff.getEmployeeId());
            dto.setPosition(staff.getPosition());
            dto.setHireDate(staff.getHireDate());
            dto.setSalary(staff.getSalary());
            dto.setIsActive(staff.getIsActive());

            if (staff.getClinic() != null) {
                dto.setClinicId(staff.getClinic().getId());
                dto.setClinicName(staff.getClinic().getClinicName());
            }
        }

        return dto;
    }

    private void handleRoleTransition(User user, Role targetRole) {
        // Update user role and status
        user.setRole(targetRole);

        // Set appropriate status based on target role
        if (targetRole == Role.DOCTOR || targetRole == Role.CLINIC) {
            user.setStatus(UserStatus.PENDING_APPROVAL);
        } else {
            user.setStatus(UserStatus.ACTIVE);
        }

        // Save updated user
        userRepository.save(user);

        // Note: Role-specific entities (Doctor, Patient, etc.) will need to be created
        // separately through their respective registration/profile completion processes
    }

    private User createUserByRole(AddUserRequest request) {
        switch (request.getRole()) {
            case PATIENT:
                return createPatientUser(request);
            case DOCTOR:
                return createDoctorUser(request);
            case CLINIC:
                return createClinicUser(request);
            case ADMIN:
                return createAdminUser(request);
            case CLINIC_STAFF:
                return createClinicStaffUser(request);
            default:
                throw new IllegalArgumentException("Unsupported role: " + request.getRole());
        }
    }

    private Patient createPatientUser(AddUserRequest request) {
        Patient patient = new Patient();
        setBasicUserInfo(patient, request);
        patient.setRole(Role.PATIENT);
        patient.setStatus(UserStatus.ACTIVE);

        // Set patient-specific fields
        patient.setPatientId(request.getEmail().split("@")[0] + "_" + System.currentTimeMillis());
        patient.setEmergencyContactName(request.getEmergencyContactName() != null ?
            request.getEmergencyContactName() : "To be updated");
        patient.setEmergencyContactPhone(request.getEmergencyContactPhone() != null ?
            request.getEmergencyContactPhone() : "To be updated");
        patient.setBloodGroup(request.getBloodGroup() != null ? request.getBloodGroup() : "Unknown");
        patient.setAllergies(request.getAllergies() != null ? request.getAllergies() : "None known");
        patient.setMedicalHistory(request.getMedicalHistory() != null ?
            request.getMedicalHistory() : "To be updated");
        patient.setInsuranceProvider(request.getInsuranceProvider());
        patient.setInsuranceNumber(request.getInsuranceNumber());

        return patientRepository.save(patient);
    }

    private Doctor createDoctorUser(AddUserRequest request) {
        Doctor doctor = new Doctor();
        setBasicUserInfo(doctor, request);
        doctor.setRole(Role.DOCTOR);
        doctor.setStatus(UserStatus.PENDING_APPROVAL);

        // Set doctor-specific fields
        doctor.setMedicalLicense(request.getMedicalLicense() != null ?
            request.getMedicalLicense() : "TEMP_" + System.currentTimeMillis());
        doctor.setYearsOfExperience(request.getYearsOfExperience() != null ?
            request.getYearsOfExperience() : 0);
        doctor.setQualification(request.getQualification() != null ?
            request.getQualification() : "To be updated");
        doctor.setDoctorStatus(DoctorStatus.PENDING_APPROVAL);
        doctor.setConsultationFee(request.getConsultationFee() != null ?
            request.getConsultationFee() : 100.0);
        doctor.setBio(request.getBio() != null ? request.getBio() : "Profile to be completed");

        return doctorRepository.save(doctor);
    }

    private Clinic createClinicUser(AddUserRequest request) {
        Clinic clinic = new Clinic();
        setBasicUserInfo(clinic, request);
        clinic.setRole(Role.CLINIC);
        clinic.setStatus(UserStatus.PENDING_APPROVAL);

        // Set clinic-specific fields
        clinic.setClinicName(request.getClinicName() != null ?
            request.getClinicName() : request.getName() + " Clinic");
        clinic.setLicenseNumber(request.getLicenseNumber() != null ?
            request.getLicenseNumber() : "CLINIC_" + System.currentTimeMillis());
        clinic.setDescription(request.getDescription() != null ?
            request.getDescription() : "Clinic profile to be completed");
        clinic.setClinicStatus(ClinicStatus.PENDING_APPROVAL);
        clinic.setOperatingHours(request.getOperatingHours() != null ?
            request.getOperatingHours() : "Mon-Fri: 9:00 AM - 5:00 PM");
        clinic.setEmergencyContact(request.getEmergencyContact() != null ?
            request.getEmergencyContact() : request.getPhoneNumber());
        clinic.setWebsiteUrl(request.getWebsiteUrl());

        return clinicRepository.save(clinic);
    }

    private Admin createAdminUser(AddUserRequest request) {
        Admin admin = new Admin();
        setBasicUserInfo(admin, request);
        admin.setRole(Role.ADMIN);
        admin.setStatus(UserStatus.ACTIVE);

        // Set admin-specific fields
        admin.setEmployeeId(request.getEmployeeId() != null ?
            request.getEmployeeId() : "ADM_" + System.currentTimeMillis());
        admin.setDepartment(request.getDepartment() != null ?
            request.getDepartment() : "Administration");
        admin.setAccessLevel(request.getAccessLevel() != null ?
            request.getAccessLevel() : "ADMIN");

        return userRepository.save(admin);
    }

    private ClinicStaff createClinicStaffUser(AddUserRequest request) {
        ClinicStaff staff = new ClinicStaff();
        setBasicUserInfo(staff, request);
        staff.setRole(Role.CLINIC_STAFF);
        staff.setStatus(UserStatus.ACTIVE);

        // Set staff-specific fields
        staff.setEmployeeId("STAFF_" + System.currentTimeMillis());
        staff.setPosition(request.getPosition() != null ?
            request.getPosition() : "Staff Member");
        staff.setHireDate(request.getHireDate() != null ?
            request.getHireDate() : java.time.LocalDate.now().toString());
        staff.setSalary(request.getSalary() != null ? request.getSalary() : 30000.0);
        staff.setIsActive(request.getIsActive() != null ? request.getIsActive() : true);

        if (request.getClinicId() != null) {
            Clinic clinic = clinicRepository.findById(request.getClinicId()).orElse(null);
            staff.setClinic(clinic);
        }

        return userRepository.save(staff);
    }

    private void setBasicUserInfo(User user, AddUserRequest request) {
        user.setName(request.getName());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setPhoneNumber(request.getPhoneNumber());
        user.setAddress(request.getAddress());
        user.setDateOfBirth(request.getDateOfBirth());
        user.setGender(request.getGender());
        user.setCreatedAt(java.time.LocalDateTime.now());
        user.setUpdatedAt(java.time.LocalDateTime.now());
    }
}
