package com.example.MediConnect.service.impl;

import com.example.MediConnect.dto.request.LoginRequest;
import com.example.MediConnect.dto.request.RegisterRequest;
import com.example.MediConnect.dto.response.AuthResponse;
import com.example.MediConnect.entity.*;
import com.example.MediConnect.enums.Role;
import com.example.MediConnect.enums.UserStatus;
import com.example.MediConnect.exception.ResourceNotFoundException;
import com.example.MediConnect.exception.UnauthorizedException;
import com.example.MediConnect.repository.*;
import com.example.MediConnect.security.JwtUtil;
import com.example.MediConnect.service.AuthService;
import com.example.MediConnect.util.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.UUID;

@Service
@Transactional
public class AuthServiceImpl implements AuthService {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private AdminRepository adminRepository;
    
    @Autowired
    private DoctorRepository doctorRepository;
    
    @Autowired
    private ClinicRepository clinicRepository;
    
    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private SpecialityRepository specialityRepository;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    @Override
    public AuthResponse login(LoginRequest loginRequest) {
        User user = userRepository.findByEmail(loginRequest.getEmail())
                .orElseThrow(() -> new ResourceNotFoundException(Constants.INVALID_CREDENTIALS));
        
        if (!passwordEncoder.matches(loginRequest.getPassword(), user.getPassword())) {
            throw new UnauthorizedException(Constants.INVALID_CREDENTIALS);
        }
        
        if (user.getStatus() != UserStatus.ACTIVE) {
            throw new UnauthorizedException("Account is not active. Please contact administrator.");
        }
        
        // Update last login
        user.setLastLogin(LocalDateTime.now());
        userRepository.save(user);
        
        // Generate JWT token
        String token = jwtUtil.generateToken(user.getEmail(), user.getRole().toString(), user.getId());
        
        return new AuthResponse(token, user.getEmail(), user.getRole().toString(), user.getId(), user.getName());
    }
    
    @Override
    public AuthResponse register(RegisterRequest registerRequest) {
        // Check if email already exists
        if (userRepository.existsByEmail(registerRequest.getEmail())) {
            throw new IllegalArgumentException(Constants.EMAIL_ALREADY_EXISTS);
        }
        
        User user = createUserByRole(registerRequest);
        user = userRepository.save(user);
        
        // Generate JWT token
        String token = jwtUtil.generateToken(user.getEmail(), user.getRole().toString(), user.getId());
        
        return new AuthResponse(token, user.getEmail(), user.getRole().toString(), user.getId(), user.getName());
    }
    
    private User createUserByRole(RegisterRequest request) {
        User user;
        
        switch (request.getRole()) {
            case ADMIN:
                user = createAdmin(request);
                break;
            case DOCTOR:
                user = createDoctor(request);
                break;
            case CLINIC:
                user = createClinic(request);
                break;
            case PATIENT:
                user = createPatient(request);
                break;
            default:
                throw new IllegalArgumentException("Invalid role: " + request.getRole());
        }
        
        return user;
    }
    
    private Admin createAdmin(RegisterRequest request) {
        Admin admin = new Admin();
        setCommonUserFields(admin, request);
        admin.setRole(Role.ADMIN);
        admin.setStatus(UserStatus.ACTIVE); // Admins are active by default
        admin.setEmployeeId(request.getEmployeeId());
        admin.setDepartment(request.getDepartment());
        admin.setAccessLevel(request.getAccessLevel());
        return admin;
    }
    
    private Doctor createDoctor(RegisterRequest request) {
        Doctor doctor = new Doctor();
        setCommonUserFields(doctor, request);
        doctor.setRole(Role.DOCTOR);
        doctor.setMedicalLicense(request.getMedicalLicense());
        doctor.setYearsOfExperience(request.getYearsOfExperience());
        doctor.setQualification(request.getQualification());
        doctor.setConsultationFee(request.getConsultationFee());
        doctor.setBio(request.getBio());
        
        if (request.getSpecialization() != null) {
            // Find or create speciality
            // This is simplified - in real implementation, you'd handle this better
        }
        
        if (request.getClinicId() != null) {
            Clinic clinic = clinicRepository.findById(request.getClinicId())
                    .orElseThrow(() -> new ResourceNotFoundException("Clinic", "id", request.getClinicId()));
            doctor.setClinic(clinic);
        }
        
        return doctor;
    }
    
    private Clinic createClinic(RegisterRequest request) {
        Clinic clinic = new Clinic();
        setCommonUserFields(clinic, request);
        clinic.setRole(Role.CLINIC);
        clinic.setClinicName(request.getClinicName());
        clinic.setLicenseNumber(request.getLicenseNumber());
        clinic.setDescription(request.getDescription());
        clinic.setOperatingHours(request.getOperatingHours());
        clinic.setEmergencyContact(request.getEmergencyContact());
        clinic.setWebsiteUrl(request.getWebsiteUrl());
        return clinic;
    }
    
    private Patient createPatient(RegisterRequest request) {
        Patient patient = new Patient();
        setCommonUserFields(patient, request);
        patient.setRole(Role.PATIENT);
        patient.setStatus(UserStatus.ACTIVE); // Patients are active by default
        patient.setPatientId(generatePatientId());
        patient.setEmergencyContactName(request.getEmergencyContactName());
        patient.setEmergencyContactPhone(request.getEmergencyContactPhone());
        patient.setBloodGroup(request.getBloodGroup());
        patient.setAllergies(request.getAllergies());
        patient.setMedicalHistory(request.getMedicalHistory());
        patient.setInsuranceProvider(request.getInsuranceProvider());
        patient.setInsuranceNumber(request.getInsuranceNumber());
        return patient;
    }
    
    private void setCommonUserFields(User user, RegisterRequest request) {
        user.setName(request.getName());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setPhoneNumber(request.getPhoneNumber());
        user.setAddress(request.getAddress());
        user.setDateOfBirth(request.getDateOfBirth());
        user.setGender(request.getGender());
    }
    
    private String generatePatientId() {
        return "PAT" + System.currentTimeMillis();
    }
    
    @Override
    public void logout(String token) {
        // In a real implementation, you might want to blacklist the token
        // For now, we'll just validate it
        if (!jwtUtil.validateToken(token)) {
            throw new UnauthorizedException(Constants.INVALID_TOKEN);
        }
    }
    
    @Override
    public boolean validateToken(String token) {
        return jwtUtil.validateToken(token);
    }
    
    @Override
    public String refreshToken(String token) {
        if (!jwtUtil.validateToken(token)) {
            throw new UnauthorizedException(Constants.INVALID_TOKEN);
        }
        
        String email = jwtUtil.getEmailFromToken(token);
        String role = jwtUtil.getRoleFromToken(token);
        Long userId = jwtUtil.getUserIdFromToken(token);
        
        return jwtUtil.generateToken(email, role, userId);
    }
}
