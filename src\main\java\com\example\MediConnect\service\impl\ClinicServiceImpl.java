package com.example.MediConnect.service.impl;

import com.example.MediConnect.dto.*;
import com.example.MediConnect.dto.request.AnnouncementRequest;
import com.example.MediConnect.dto.request.DoctorRequest;
import com.example.MediConnect.entity.*;
import com.example.MediConnect.enums.AppointmentStatus;
import com.example.MediConnect.enums.Role;
import com.example.MediConnect.enums.UserStatus;
import com.example.MediConnect.exception.ResourceNotFoundException;
import com.example.MediConnect.repository.*;
import com.example.MediConnect.service.ClinicService;
import com.example.MediConnect.util.Constants;
import com.example.MediConnect.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class ClinicServiceImpl implements ClinicService {
    
    @Autowired
    private ClinicRepository clinicRepository;
    
    @Autowired
    private DoctorRepository doctorRepository;
    
    @Autowired
    private ClinicStaffRepository clinicStaffRepository;
    
    @Autowired
    private AppointmentRepository appointmentRepository;
    
    @Autowired
    private AnnouncementRepository announcementRepository;
    
    @Autowired
    private SpecialityRepository specialityRepository;
    
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    @Override
    public ClinicDTO getClinicProfile(Long clinicId) {
        Clinic clinic = clinicRepository.findById(clinicId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.CLINIC_NOT_FOUND));
        return convertToClinicDTO(clinic);
    }
    
    @Override
    public ClinicDTO updateClinicProfile(Long clinicId, ClinicDTO clinicDTO) {
        Clinic clinic = clinicRepository.findById(clinicId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.CLINIC_NOT_FOUND));
        
        clinic.setName(clinicDTO.getName());
        clinic.setEmail(clinicDTO.getEmail());
        clinic.setPhoneNumber(clinicDTO.getPhoneNumber());
        clinic.setAddress(clinicDTO.getAddress());
        clinic.setClinicName(clinicDTO.getClinicName());
        clinic.setDescription(clinicDTO.getDescription());
        clinic.setOperatingHours(clinicDTO.getOperatingHours());
        clinic.setEmergencyContact(clinicDTO.getEmergencyContact());
        clinic.setWebsiteUrl(clinicDTO.getWebsiteUrl());
        
        clinic = clinicRepository.save(clinic);
        return convertToClinicDTO(clinic);
    }
    
    @Override
    public List<User> getClinicStaff(Long clinicId) {
        List<ClinicStaff> clinicStaff = clinicStaffRepository.findByClinicIdAndIsActive(clinicId, true);
        List<Doctor> doctors = doctorRepository.findByClinicId(clinicId);
        
        List<User> allStaff = clinicStaff.stream()
                .map(staff -> (User) staff)
                .collect(Collectors.toList());
        
        allStaff.addAll(doctors.stream()
                .map(doctor -> (User) doctor)
                .collect(Collectors.toList()));
        
        return allStaff;
    }
    
    @Override
    public DoctorDTO addDoctorToClinic(Long clinicId, DoctorRequest doctorRequest) {
        Clinic clinic = clinicRepository.findById(clinicId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.CLINIC_NOT_FOUND));
        
        // Check if doctor with this email already exists
        if (doctorRepository.findByEmail(doctorRequest.getEmail()).isPresent()) {
            throw new IllegalArgumentException("Doctor with this email already exists");
        }
        
        Doctor doctor = new Doctor();
        doctor.setName(doctorRequest.getName());
        doctor.setEmail(doctorRequest.getEmail());
        doctor.setPassword(passwordEncoder.encode("doctor123")); // Default password
        doctor.setPhoneNumber(doctorRequest.getPhoneNumber());
        doctor.setAddress(doctorRequest.getAddress());
        doctor.setDateOfBirth(doctorRequest.getDateOfBirth());
        doctor.setGender(doctorRequest.getGender());
        doctor.setRole(Role.DOCTOR);
        doctor.setStatus(UserStatus.ACTIVE);
        doctor.setMedicalLicense(doctorRequest.getMedicalLicense());
        doctor.setYearsOfExperience(doctorRequest.getYearsOfExperience());
        doctor.setQualification(doctorRequest.getQualification());
        doctor.setConsultationFee(doctorRequest.getConsultationFee());
        doctor.setBio(doctorRequest.getBio());
        doctor.setClinic(clinic);
        
        if (doctorRequest.getSpecialityId() != null) {
            Speciality speciality = specialityRepository.findById(doctorRequest.getSpecialityId())
                    .orElseThrow(() -> new ResourceNotFoundException("Speciality not found"));
            doctor.setSpeciality(speciality);
        }
        
        doctor = doctorRepository.save(doctor);
        return convertToDoctorDTO(doctor);
    }
    
    @Override
    public void removeDoctorFromClinic(Long clinicId, Long doctorId) {
        Doctor doctor = doctorRepository.findById(doctorId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.DOCTOR_NOT_FOUND));
        
        if (!doctor.getClinic().getId().equals(clinicId)) {
            throw new IllegalArgumentException("Doctor does not belong to this clinic");
        }
        
        doctor.setClinic(null);
        doctorRepository.save(doctor);
    }
    
    @Override
    public User addClinicStaff(Long clinicId, User staffMember) {
        Clinic clinic = clinicRepository.findById(clinicId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.CLINIC_NOT_FOUND));
        
        ClinicStaff clinicStaff = new ClinicStaff();
        clinicStaff.setName(staffMember.getName());
        clinicStaff.setEmail(staffMember.getEmail());
        clinicStaff.setPassword(passwordEncoder.encode("staff123")); // Default password
        clinicStaff.setPhoneNumber(staffMember.getPhoneNumber());
        clinicStaff.setAddress(staffMember.getAddress());
        clinicStaff.setRole(Role.CLINIC_STAFF);
        clinicStaff.setStatus(UserStatus.ACTIVE);
        clinicStaff.setClinic(clinic);
        clinicStaff.setEmployeeId("EMP" + System.currentTimeMillis());
        clinicStaff.setPosition("Staff");
        clinicStaff.setHireDate(DateUtil.formatDate(LocalDate.now()));
        
        return clinicStaffRepository.save(clinicStaff);
    }
    
    @Override
    public void removeClinicStaff(Long clinicId, Long staffId) {
        ClinicStaff staff = clinicStaffRepository.findById(staffId)
                .orElseThrow(() -> new ResourceNotFoundException("Staff member not found"));
        
        if (!staff.getClinic().getId().equals(clinicId)) {
            throw new IllegalArgumentException("Staff member does not belong to this clinic");
        }
        
        staff.setIsActive(false);
        clinicStaffRepository.save(staff);
    }
    
    @Override
    public List<AppointmentDTO> getClinicAppointments(Long clinicId) {
        List<Appointment> appointments = appointmentRepository.findByClinicId(clinicId);
        return appointments.stream()
                .map(this::convertToAppointmentDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AppointmentDTO> getUpcomingAppointments(Long clinicId) {
        LocalDateTime now = LocalDateTime.now();
        List<Appointment> appointments = appointmentRepository.findByClinicIdAndStatus(clinicId, AppointmentStatus.SCHEDULED);
        return appointments.stream()
                .filter(apt -> apt.getAppointmentDate().isAfter(now))
                .map(this::convertToAppointmentDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AppointmentDTO> getTodayAppointments(Long clinicId) {
        LocalDateTime startOfDay = DateUtil.getStartOfDay(LocalDate.now());
        LocalDateTime endOfDay = DateUtil.getEndOfDay(LocalDate.now());
        
        List<Appointment> appointments = appointmentRepository.findByClinicId(clinicId);
        return appointments.stream()
                .filter(apt -> apt.getAppointmentDate().isAfter(startOfDay) && 
                              apt.getAppointmentDate().isBefore(endOfDay))
                .map(this::convertToAppointmentDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<AnnouncementDTO> getClinicAnnouncements(Long clinicId) {
        List<Announcement> announcements = announcementRepository.findByClinicIdOrderByCreatedAtDesc(clinicId);
        return announcements.stream()
                .map(this::convertToAnnouncementDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public AnnouncementDTO createAnnouncement(Long clinicId, AnnouncementRequest announcementRequest) {
        Clinic clinic = clinicRepository.findById(clinicId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.CLINIC_NOT_FOUND));
        
        Announcement announcement = new Announcement();
        announcement.setClinic(clinic);
        announcement.setTitle(announcementRequest.getTitle());
        announcement.setContent(announcementRequest.getContent());
        announcement.setPriority(announcementRequest.getPriority());
        announcement.setIsActive(announcementRequest.getIsActive());
        announcement.setStartDate(announcementRequest.getStartDate());
        announcement.setEndDate(announcementRequest.getEndDate());
        
        announcement = announcementRepository.save(announcement);
        return convertToAnnouncementDTO(announcement);
    }
    
    @Override
    public AnnouncementDTO updateAnnouncement(Long announcementId, AnnouncementRequest announcementRequest) {
        Announcement announcement = announcementRepository.findById(announcementId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.ANNOUNCEMENT_NOT_FOUND));
        
        announcement.setTitle(announcementRequest.getTitle());
        announcement.setContent(announcementRequest.getContent());
        announcement.setPriority(announcementRequest.getPriority());
        announcement.setIsActive(announcementRequest.getIsActive());
        announcement.setStartDate(announcementRequest.getStartDate());
        announcement.setEndDate(announcementRequest.getEndDate());
        
        announcement = announcementRepository.save(announcement);
        return convertToAnnouncementDTO(announcement);
    }
    
    @Override
    public void deleteAnnouncement(Long announcementId) {
        Announcement announcement = announcementRepository.findById(announcementId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.ANNOUNCEMENT_NOT_FOUND));
        announcementRepository.delete(announcement);
    }
    
    // Helper methods for DTO conversion
    private ClinicDTO convertToClinicDTO(Clinic clinic) {
        ClinicDTO dto = new ClinicDTO();
        dto.setId(clinic.getId());
        dto.setName(clinic.getName());
        dto.setEmail(clinic.getEmail());
        dto.setPhoneNumber(clinic.getPhoneNumber());
        dto.setAddress(clinic.getAddress());
        dto.setClinicName(clinic.getClinicName());
        dto.setDescription(clinic.getDescription());
        dto.setClinicStatus(clinic.getClinicStatus());
        dto.setOperatingHours(clinic.getOperatingHours());
        dto.setEmergencyContact(clinic.getEmergencyContact());
        dto.setWebsiteUrl(clinic.getWebsiteUrl());
        dto.setCreatedAt(clinic.getCreatedAt());
        dto.setUpdatedAt(clinic.getUpdatedAt());
        
        if (clinic.getDoctors() != null) {
            dto.setTotalDoctors(clinic.getDoctors().size());
        }
        if (clinic.getClinicStaff() != null) {
            dto.setTotalStaff(clinic.getClinicStaff().size());
        }
        
        return dto;
    }
    
    private DoctorDTO convertToDoctorDTO(Doctor doctor) {
        DoctorDTO dto = new DoctorDTO();
        dto.setId(doctor.getId());
        dto.setName(doctor.getName());
        dto.setEmail(doctor.getEmail());
        dto.setPhoneNumber(doctor.getPhoneNumber());
        dto.setAddress(doctor.getAddress());
        dto.setDateOfBirth(doctor.getDateOfBirth());
        dto.setGender(doctor.getGender());
        dto.setMedicalLicense(doctor.getMedicalLicense());
        dto.setYearsOfExperience(doctor.getYearsOfExperience());
        dto.setQualification(doctor.getQualification());
        dto.setDoctorStatus(doctor.getDoctorStatus());
        dto.setConsultationFee(doctor.getConsultationFee());
        dto.setBio(doctor.getBio());
        dto.setCreatedAt(doctor.getCreatedAt());
        dto.setUpdatedAt(doctor.getUpdatedAt());
        
        if (doctor.getSpeciality() != null) {
            dto.setSpecialtyName(doctor.getSpeciality().getName().toString());
        }
        
        if (doctor.getClinic() != null) {
            dto.setClinicId(doctor.getClinic().getId());
            dto.setClinicName(doctor.getClinic().getClinicName());
        }
        
        return dto;
    }
    
    private AppointmentDTO convertToAppointmentDTO(Appointment appointment) {
        AppointmentDTO dto = new AppointmentDTO();
        dto.setId(appointment.getId());
        dto.setAppointmentDate(appointment.getAppointmentDate());
        dto.setStatus(appointment.getStatus());
        dto.setReason(appointment.getReason());
        dto.setNotes(appointment.getNotes());
        dto.setDurationMinutes(appointment.getDurationMinutes());
        dto.setCreatedAt(appointment.getCreatedAt());
        dto.setUpdatedAt(appointment.getUpdatedAt());
        dto.setCancellationReason(appointment.getCancellationReason());
        
        if (appointment.getPatient() != null) {
            dto.setPatientId(appointment.getPatient().getId());
            dto.setPatientName(appointment.getPatient().getName());
            dto.setPatientEmail(appointment.getPatient().getEmail());
        }
        
        if (appointment.getDoctor() != null) {
            dto.setDoctorId(appointment.getDoctor().getId());
            dto.setDoctorName(appointment.getDoctor().getName());
            if (appointment.getDoctor().getSpeciality() != null) {
                dto.setDoctorSpecialty(appointment.getDoctor().getSpeciality().getName().toString());
            }
        }
        
        if (appointment.getClinic() != null) {
            dto.setClinicId(appointment.getClinic().getId());
            dto.setClinicName(appointment.getClinic().getClinicName());
        }
        
        return dto;
    }
    
    private AnnouncementDTO convertToAnnouncementDTO(Announcement announcement) {
        AnnouncementDTO dto = new AnnouncementDTO();
        dto.setId(announcement.getId());
        dto.setTitle(announcement.getTitle());
        dto.setContent(announcement.getContent());
        dto.setPriority(announcement.getPriority());
        dto.setIsActive(announcement.getIsActive());
        dto.setStartDate(announcement.getStartDate());
        dto.setEndDate(announcement.getEndDate());
        dto.setCreatedAt(announcement.getCreatedAt());
        dto.setUpdatedAt(announcement.getUpdatedAt());
        
        if (announcement.getClinic() != null) {
            dto.setClinicId(announcement.getClinic().getId());
            dto.setClinicName(announcement.getClinic().getClinicName());
        }
        
        return dto;
    }
}
