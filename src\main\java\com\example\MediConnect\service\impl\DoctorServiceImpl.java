package com.example.MediConnect.service.impl;

import com.example.MediConnect.dto.*;
import com.example.MediConnect.dto.request.DiagnosisRequest;
import com.example.MediConnect.dto.request.DoctorRequest;
import com.example.MediConnect.dto.request.FollowUpRequest;
import com.example.MediConnect.dto.request.AppointmentRequest;
import com.example.MediConnect.entity.*;
import com.example.MediConnect.enums.AppointmentStatus;
import com.example.MediConnect.enums.DoctorStatus;
import com.example.MediConnect.enums.FollowUpStatus;
import com.example.MediConnect.exception.ResourceNotFoundException;
import com.example.MediConnect.repository.*;
import com.example.MediConnect.service.DoctorService;
import com.example.MediConnect.util.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Transactional
public class DoctorServiceImpl implements DoctorService {
    
    @Autowired
    private DoctorRepository doctorRepository;
    
    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private DiagnosisRepository diagnosisRepository;
    
    @Autowired
    private PrescriptionRepository prescriptionRepository;
    
    @Autowired
    private FollowUpRepository followUpRepository;
    
    @Autowired
    private SpecialityRepository specialityRepository;
    
    @Autowired
    private AppointmentRepository appointmentRepository;

    @Autowired
    private ClinicRepository clinicRepository;

    @Override
    public DoctorDTO getDoctorProfile(Long doctorId) {
        Doctor doctor = doctorRepository.findById(doctorId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.DOCTOR_NOT_FOUND));
        return convertToDoctorDTO(doctor);
    }
    
    @Override
    public DoctorDTO updateDoctorProfile(Long doctorId, DoctorRequest doctorRequest) {
        Doctor doctor = doctorRepository.findById(doctorId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.DOCTOR_NOT_FOUND));
        
        doctor.setName(doctorRequest.getName());
        doctor.setEmail(doctorRequest.getEmail());
        doctor.setPhoneNumber(doctorRequest.getPhoneNumber());
        doctor.setAddress(doctorRequest.getAddress());
        doctor.setDateOfBirth(doctorRequest.getDateOfBirth());
        doctor.setGender(doctorRequest.getGender());
        doctor.setMedicalLicense(doctorRequest.getMedicalLicense());
        doctor.setYearsOfExperience(doctorRequest.getYearsOfExperience());
        doctor.setQualification(doctorRequest.getQualification());
        doctor.setConsultationFee(doctorRequest.getConsultationFee());
        doctor.setBio(doctorRequest.getBio());
        
        if (doctorRequest.getSpecialityId() != null) {
            Speciality speciality = specialityRepository.findById(doctorRequest.getSpecialityId())
                    .orElseThrow(() -> new ResourceNotFoundException("Speciality not found"));
            doctor.setSpeciality(speciality);
        }
        
        doctor = doctorRepository.save(doctor);
        return convertToDoctorDTO(doctor);
    }
    
    @Override
    public List<PatientDTO> getAssignedPatients(Long doctorId) {
        List<Patient> patients = appointmentRepository.findDistinctPatientsByDoctorId(doctorId);
        return patients.stream()
                .map(this::convertToPatientDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getDoctorPatientStatistics(Long doctorId) {
        Long totalPatients = appointmentRepository.countDistinctPatientsByDoctorId(doctorId);
        Long activeAppointments = appointmentRepository.countActiveAppointmentsByDoctorId(doctorId);
        Long newPatients = appointmentRepository.countNewPatientsByDoctorIdSince(doctorId,
            LocalDateTime.now().minusDays(30));

        List<Patient> patients = appointmentRepository.findDistinctPatientsByDoctorId(doctorId);

        Map<String, Object> stats = new HashMap<>();
        stats.put("totalPatients", totalPatients);
        stats.put("activePatients", activeAppointments);
        stats.put("newPatients", newPatients);
        stats.put("inactivePatients", totalPatients - activeAppointments);
        stats.put("averageAge", calculateAverageAge(patients));
        stats.put("genderDistribution", calculateGenderDistribution(patients));

        return stats;
    }

    @Override
    public List<AppointmentDTO> getTodayAppointments(Long doctorId) {
        List<Appointment> appointments = appointmentRepository.findTodayAppointmentsByDoctorId(doctorId);
        return appointments.stream()
                .map(this::convertToAppointmentDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<AppointmentDTO> getUpcomingAppointments(Long doctorId) {
        List<Appointment> appointments = appointmentRepository.findUpcomingAppointmentsByDoctorId(doctorId);
        return appointments.stream()
                .map(this::convertToAppointmentDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<AppointmentDTO> getCompletedAppointments(Long doctorId) {
        List<Appointment> appointments = appointmentRepository.findCompletedAppointmentsByDoctorId(doctorId);
        return appointments.stream()
                .map(this::convertToAppointmentDTO)
                .collect(Collectors.toList());
    }

    @Override
    public AppointmentDTO getAppointmentDetails(Long doctorId, Long appointmentId) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.APPOINTMENT_NOT_FOUND));

        // Verify the appointment belongs to the doctor
        if (!appointment.getDoctor().getId().equals(doctorId)) {
            throw new IllegalArgumentException("Appointment does not belong to the specified doctor");
        }

        return convertToAppointmentDTO(appointment);
    }

    @Override
    public AppointmentDTO rescheduleAppointment(Long doctorId, Long appointmentId, LocalDateTime newDateTime, String reason) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.APPOINTMENT_NOT_FOUND));

        // Verify the appointment belongs to the doctor
        if (!appointment.getDoctor().getId().equals(doctorId)) {
            throw new IllegalArgumentException("Appointment does not belong to the specified doctor");
        }

        // Check if new slot is available
        if (!isSlotAvailable(doctorId, newDateTime, appointment.getDurationMinutes())) {
            throw new IllegalArgumentException("The requested time slot is not available");
        }

        appointment.setAppointmentDate(newDateTime);
        appointment.setStatus(AppointmentStatus.SCHEDULED);
        appointment.setNotes(appointment.getNotes() + "\nRescheduled: " + reason);

        appointment = appointmentRepository.save(appointment);
        return convertToAppointmentDTO(appointment);
    }

    @Override
    public AppointmentDTO updateAppointmentNotes(Long doctorId, Long appointmentId, String notes, String diagnosis) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.APPOINTMENT_NOT_FOUND));

        // Verify the appointment belongs to the doctor
        if (!appointment.getDoctor().getId().equals(doctorId)) {
            throw new IllegalArgumentException("Appointment does not belong to the specified doctor");
        }

        appointment.setNotes(notes);
        appointment = appointmentRepository.save(appointment);

        // If diagnosis is provided, create or update diagnosis record
        if (diagnosis != null && !diagnosis.trim().isEmpty()) {
            createOrUpdateDiagnosis(appointment, diagnosis);
        }

        return convertToAppointmentDTO(appointment);
    }

    @Override
    public AppointmentDTO scheduleAppointment(Long doctorId, Long patientId, AppointmentRequest appointmentRequest) {
        Doctor doctor = doctorRepository.findById(doctorId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.DOCTOR_NOT_FOUND));

        Patient patient = patientRepository.findById(patientId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.PATIENT_NOT_FOUND));

        // Check if slot is available
        if (!isSlotAvailable(doctorId, appointmentRequest.getAppointmentDate(), appointmentRequest.getDurationMinutes())) {
            throw new IllegalArgumentException(Constants.APPOINTMENT_SLOT_NOT_AVAILABLE);
        }

        Appointment appointment = new Appointment();
        appointment.setDoctor(doctor);
        appointment.setPatient(patient);

        // Set clinic - if doctor doesn't have a clinic, use a default one or leave null
        if (doctor.getClinic() != null) {
            appointment.setClinic(doctor.getClinic());
        } else {
            // For now, we'll find the first available clinic or create a default one
            // In a real application, this should be handled differently
            Clinic defaultClinic = clinicRepository.findAll().stream().findFirst().orElse(null);
            appointment.setClinic(defaultClinic);
        }

        appointment.setAppointmentDate(appointmentRequest.getAppointmentDate());
        appointment.setReason(appointmentRequest.getReason());
        appointment.setNotes(appointmentRequest.getNotes());
        appointment.setDurationMinutes(appointmentRequest.getDurationMinutes() != null ?
                appointmentRequest.getDurationMinutes() : Constants.DEFAULT_APPOINTMENT_DURATION);
        appointment.setStatus(AppointmentStatus.SCHEDULED);

        appointment = appointmentRepository.save(appointment);
        return convertToAppointmentDTO(appointment);
    }

    @Override
    public PatientDTO getPatientDetails(Long patientId) {
        Patient patient = patientRepository.findById(patientId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.PATIENT_NOT_FOUND));
        return convertToPatientDTO(patient);
    }
    
    @Override
    public DiagnosisDTO logPatientDiagnosis(DiagnosisRequest diagnosisRequest) {
        Patient patient = patientRepository.findById(diagnosisRequest.getPatientId())
                .orElseThrow(() -> new ResourceNotFoundException(Constants.PATIENT_NOT_FOUND));
        
        Doctor doctor = doctorRepository.findById(diagnosisRequest.getDoctorId())
                .orElseThrow(() -> new ResourceNotFoundException(Constants.DOCTOR_NOT_FOUND));
        
        Diagnosis diagnosis = new Diagnosis();
        diagnosis.setPatient(patient);
        diagnosis.setDoctor(doctor);
        diagnosis.setDiagnosisCode(diagnosisRequest.getDiagnosisCode());
        diagnosis.setDiagnosisDescription(diagnosisRequest.getDiagnosisDescription());
        diagnosis.setSymptoms(diagnosisRequest.getSymptoms());
        diagnosis.setTreatmentPlan(diagnosisRequest.getTreatmentPlan());
        diagnosis.setDoctorNotes(diagnosisRequest.getDoctorNotes());
        
        if (diagnosisRequest.getAppointmentId() != null) {
            Appointment appointment = appointmentRepository.findById(diagnosisRequest.getAppointmentId())
                    .orElse(null);
            diagnosis.setAppointment(appointment);
        }
        
        diagnosis = diagnosisRepository.save(diagnosis);
        
        // Save prescriptions if provided
        if (diagnosisRequest.getPrescriptions() != null && !diagnosisRequest.getPrescriptions().isEmpty()) {
            for (DiagnosisRequest.PrescriptionRequest prescReq : diagnosisRequest.getPrescriptions()) {
                Prescription prescription = new Prescription();
                prescription.setDiagnosis(diagnosis);
                prescription.setMedicationName(prescReq.getMedicationName());
                prescription.setDosage(prescReq.getDosage());
                prescription.setFrequency(prescReq.getFrequency());
                prescription.setDuration(prescReq.getDuration());
                prescription.setInstructions(prescReq.getInstructions());
                prescription.setQuantity(prescReq.getQuantity());
                prescription.setRefills(prescReq.getRefills());
                prescriptionRepository.save(prescription);
            }
        }
        
        return convertToDiagnosisDTO(diagnosis);
    }
    
    @Override
    public List<DiagnosisDTO> getPatientMedicalHistory(Long patientId) {
        List<Diagnosis> diagnoses = diagnosisRepository.findByPatientIdOrderByCreatedAtDesc(patientId);
        return diagnoses.stream()
                .map(this::convertToDiagnosisDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public FollowUpDTO scheduleFollowUp(FollowUpRequest followUpRequest) {
        Patient patient = patientRepository.findById(followUpRequest.getPatientId())
                .orElseThrow(() -> new ResourceNotFoundException(Constants.PATIENT_NOT_FOUND));
        
        Doctor doctor = doctorRepository.findById(followUpRequest.getDoctorId())
                .orElseThrow(() -> new ResourceNotFoundException(Constants.DOCTOR_NOT_FOUND));
        
        FollowUp followUp = new FollowUp();
        followUp.setPatient(patient);
        followUp.setDoctor(doctor);
        followUp.setScheduledDate(followUpRequest.getScheduledDate());
        followUp.setReason(followUpRequest.getReason());
        followUp.setNotes(followUpRequest.getNotes());
        
        followUp = followUpRepository.save(followUp);
        return convertToFollowUpDTO(followUp);
    }
    
    @Override
    public List<FollowUpDTO> getScheduledFollowUps(Long doctorId) {
        List<FollowUp> followUps = followUpRepository.findByDoctorIdOrderByScheduledDateAsc(doctorId);
        return followUps.stream()
                .map(this::convertToFollowUpDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public FollowUpDTO updateFollowUp(Long followUpId, FollowUpRequest followUpRequest) {
        FollowUp followUp = followUpRepository.findById(followUpId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.FOLLOWUP_NOT_FOUND));
        
        followUp.setScheduledDate(followUpRequest.getScheduledDate());
        followUp.setReason(followUpRequest.getReason());
        followUp.setNotes(followUpRequest.getNotes());
        
        followUp = followUpRepository.save(followUp);
        return convertToFollowUpDTO(followUp);
    }
    
    @Override
    public void cancelFollowUp(Long followUpId) {
        FollowUp followUp = followUpRepository.findById(followUpId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.FOLLOWUP_NOT_FOUND));
        followUpRepository.delete(followUp);
    }
    
    @Override
    public List<DoctorDTO> getDoctorsBySpecialty(Speciality speciality) {
        List<Doctor> doctors = doctorRepository.findBySpecialityAndStatus(speciality, DoctorStatus.ACTIVE);
        return doctors.stream()
                .map(this::convertToDoctorDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<DoctorDTO> getDoctorsByClinic(Long clinicId) {
        List<Doctor> doctors = doctorRepository.findByClinicIdAndStatus(clinicId, DoctorStatus.ACTIVE);
        return doctors.stream()
                .map(this::convertToDoctorDTO)
                .collect(Collectors.toList());
    }
    
    // Helper methods for DTO conversion
    private DoctorDTO convertToDoctorDTO(Doctor doctor) {
        DoctorDTO dto = new DoctorDTO();
        dto.setId(doctor.getId());
        dto.setName(doctor.getName());
        dto.setEmail(doctor.getEmail());
        dto.setPhoneNumber(doctor.getPhoneNumber());
        dto.setAddress(doctor.getAddress());
        dto.setDateOfBirth(doctor.getDateOfBirth());
        dto.setGender(doctor.getGender());
        dto.setMedicalLicense(doctor.getMedicalLicense());
        dto.setYearsOfExperience(doctor.getYearsOfExperience());
        dto.setQualification(doctor.getQualification());
        dto.setDoctorStatus(doctor.getDoctorStatus());
        dto.setConsultationFee(doctor.getConsultationFee());
        dto.setBio(doctor.getBio());
        dto.setCreatedAt(doctor.getCreatedAt());
        dto.setUpdatedAt(doctor.getUpdatedAt());
        
        if (doctor.getSpeciality() != null) {
            dto.setSpecialtyName(doctor.getSpeciality().getName().toString());
        }
        
        if (doctor.getClinic() != null) {
            dto.setClinicId(doctor.getClinic().getId());
            dto.setClinicName(doctor.getClinic().getClinicName());
        }
        
        return dto;
    }
    
    private PatientDTO convertToPatientDTO(Patient patient) {
        PatientDTO dto = new PatientDTO();
        dto.setId(patient.getId());
        dto.setName(patient.getName());
        dto.setEmail(patient.getEmail());
        dto.setPhoneNumber(patient.getPhoneNumber());
        dto.setAddress(patient.getAddress());
        dto.setDateOfBirth(patient.getDateOfBirth());
        dto.setGender(patient.getGender());
        dto.setPatientId(patient.getPatientId());
        dto.setEmergencyContactName(patient.getEmergencyContactName());
        dto.setEmergencyContactPhone(patient.getEmergencyContactPhone());
        dto.setBloodGroup(patient.getBloodGroup());
        dto.setAllergies(patient.getAllergies());
        dto.setMedicalHistory(patient.getMedicalHistory());
        dto.setInsuranceProvider(patient.getInsuranceProvider());
        dto.setInsuranceNumber(patient.getInsuranceNumber());
        dto.setCreatedAt(patient.getCreatedAt());
        dto.setUpdatedAt(patient.getUpdatedAt());
        return dto;
    }
    
    private DiagnosisDTO convertToDiagnosisDTO(Diagnosis diagnosis) {
        DiagnosisDTO dto = new DiagnosisDTO();
        dto.setId(diagnosis.getId());
        dto.setDiagnosisCode(diagnosis.getDiagnosisCode());
        dto.setDiagnosisDescription(diagnosis.getDiagnosisDescription());
        dto.setSymptoms(diagnosis.getSymptoms());
        dto.setTreatmentPlan(diagnosis.getTreatmentPlan());
        dto.setDoctorNotes(diagnosis.getDoctorNotes());
        dto.setCreatedAt(diagnosis.getCreatedAt());
        dto.setUpdatedAt(diagnosis.getUpdatedAt());
        
        if (diagnosis.getPatient() != null) {
            dto.setPatientId(diagnosis.getPatient().getId());
            dto.setPatientName(diagnosis.getPatient().getName());
        }
        
        if (diagnosis.getDoctor() != null) {
            dto.setDoctorId(diagnosis.getDoctor().getId());
            dto.setDoctorName(diagnosis.getDoctor().getName());
        }
        
        if (diagnosis.getAppointment() != null) {
            dto.setAppointmentId(diagnosis.getAppointment().getId());
        }
        
        // Load prescriptions
        if (diagnosis.getPrescriptions() != null) {
            List<PrescriptionDTO> prescriptions = diagnosis.getPrescriptions().stream()
                    .map(this::convertToPrescriptionDTO)
                    .collect(Collectors.toList());
            dto.setPrescriptions(prescriptions);
        }
        
        return dto;
    }
    
    private PrescriptionDTO convertToPrescriptionDTO(Prescription prescription) {
        PrescriptionDTO dto = new PrescriptionDTO();
        dto.setId(prescription.getId());
        dto.setDiagnosisId(prescription.getDiagnosis().getId());
        dto.setMedicationName(prescription.getMedicationName());
        dto.setDosage(prescription.getDosage());
        dto.setFrequency(prescription.getFrequency());
        dto.setDuration(prescription.getDuration());
        dto.setInstructions(prescription.getInstructions());
        dto.setQuantity(prescription.getQuantity());
        dto.setRefills(prescription.getRefills());
        dto.setCreatedAt(prescription.getCreatedAt());
        dto.setIsActive(prescription.getIsActive());
        return dto;
    }
    
    private FollowUpDTO convertToFollowUpDTO(FollowUp followUp) {
        FollowUpDTO dto = new FollowUpDTO();
        dto.setId(followUp.getId());
        dto.setScheduledDate(followUp.getScheduledDate());
        dto.setStatus(followUp.getStatus());
        dto.setReason(followUp.getReason());
        dto.setNotes(followUp.getNotes());
        dto.setCompletedDate(followUp.getCompletedDate());
        dto.setCreatedAt(followUp.getCreatedAt());
        dto.setUpdatedAt(followUp.getUpdatedAt());
        
        if (followUp.getPatient() != null) {
            dto.setPatientId(followUp.getPatient().getId());
            dto.setPatientName(followUp.getPatient().getName());
        }
        
        if (followUp.getDoctor() != null) {
            dto.setDoctorId(followUp.getDoctor().getId());
            dto.setDoctorName(followUp.getDoctor().getName());
        }
        
        return dto;
    }

    private AppointmentDTO convertToAppointmentDTO(Appointment appointment) {
        AppointmentDTO dto = new AppointmentDTO();
        dto.setId(appointment.getId());
        dto.setAppointmentDate(appointment.getAppointmentDate());
        dto.setStatus(appointment.getStatus());
        dto.setReason(appointment.getReason());
        dto.setNotes(appointment.getNotes());
        dto.setDurationMinutes(appointment.getDurationMinutes());
        dto.setCreatedAt(appointment.getCreatedAt());
        dto.setUpdatedAt(appointment.getUpdatedAt());
        dto.setCancellationReason(appointment.getCancellationReason());

        if (appointment.getPatient() != null) {
            dto.setPatientId(appointment.getPatient().getId());
            dto.setPatientName(appointment.getPatient().getName());
            dto.setPatientEmail(appointment.getPatient().getEmail());
        }

        if (appointment.getDoctor() != null) {
            dto.setDoctorId(appointment.getDoctor().getId());
            dto.setDoctorName(appointment.getDoctor().getName());
            if (appointment.getDoctor().getSpeciality() != null) {
                dto.setDoctorSpecialty(appointment.getDoctor().getSpeciality().getName().toString());
            }
        }

        if (appointment.getClinic() != null) {
            dto.setClinicId(appointment.getClinic().getId());
            dto.setClinicName(appointment.getClinic().getClinicName());
        }

        return dto;
    }

    private boolean isSlotAvailable(Long doctorId, LocalDateTime appointmentDate, Integer duration) {
        if (duration == null) {
            duration = Constants.DEFAULT_APPOINTMENT_DURATION;
        }

        LocalDateTime endTime = appointmentDate.plusMinutes(duration);

        List<Appointment> conflictingAppointments = appointmentRepository
                .findByDoctorIdAndAppointmentDateBetween(doctorId,
                        appointmentDate.minusMinutes(duration),
                        endTime);

        return conflictingAppointments.stream()
                .noneMatch(apt -> apt.getStatus() == AppointmentStatus.SCHEDULED ||
                                 apt.getStatus() == AppointmentStatus.CONFIRMED ||
                                 apt.getStatus() == AppointmentStatus.IN_PROGRESS);
    }

    private void createOrUpdateDiagnosis(Appointment appointment, String diagnosisDescription) {
        List<Diagnosis> existingDiagnoses = diagnosisRepository.findByAppointmentId(appointment.getId());

        if (!existingDiagnoses.isEmpty()) {
            // Update existing diagnosis
            Diagnosis diagnosis = existingDiagnoses.get(0);
            diagnosis.setDiagnosisDescription(diagnosisDescription);
            diagnosisRepository.save(diagnosis);
        } else {
            // Create new diagnosis
            Diagnosis diagnosis = new Diagnosis();
            diagnosis.setAppointment(appointment);
            diagnosis.setPatient(appointment.getPatient());
            diagnosis.setDoctor(appointment.getDoctor());
            diagnosis.setDiagnosisDescription(diagnosisDescription);
            diagnosisRepository.save(diagnosis);
        }
    }

    private double calculateAverageAge(List<Patient> patients) {
        return patients.stream()
                .filter(p -> p.getDateOfBirth() != null && !p.getDateOfBirth().isEmpty())
                .mapToInt(p -> {
                    try {
                        LocalDate birthDate = LocalDate.parse(p.getDateOfBirth());
                        return Period.between(birthDate, LocalDate.now()).getYears();
                    } catch (Exception e) {
                        return 0;
                    }
                })
                .average()
                .orElse(0.0);
    }

    private Map<String, Long> calculateGenderDistribution(List<Patient> patients) {
        Map<String, Long> distribution = new HashMap<>();
        distribution.put("male", patients.stream()
                .filter(p -> "MALE".equalsIgnoreCase(p.getGender()))
                .count());
        distribution.put("female", patients.stream()
                .filter(p -> "FEMALE".equalsIgnoreCase(p.getGender()))
                .count());
        distribution.put("other", patients.stream()
                .filter(p -> p.getGender() != null &&
                           !"MALE".equalsIgnoreCase(p.getGender()) &&
                           !"FEMALE".equalsIgnoreCase(p.getGender()))
                .count());
        return distribution;
    }
}
