package com.example.MediConnect.util;

public class Constants {

    // JWT Constants
    public static final String JWT_SECRET = "mediconnect_secret_key_2024_very_long_secret_key_for_security_purposes_256_bits";
    public static final int JWT_EXPIRATION_MS = 86400000; // 24 hours
    public static final String JWT_HEADER = "Authorization";
    public static final String JWT_PREFIX = "Bearer ";

    // Default Values
    public static final int DEFAULT_PAGE_SIZE = 10;
    public static final int MAX_PAGE_SIZE = 100;
    public static final String DEFAULT_SORT_BY = "id";
    public static final String DEFAULT_SORT_DIRECTION = "asc";

    // Appointment Constants
    public static final int DEFAULT_APPOINTMENT_DURATION = 30; // minutes
    public static final int MAX_APPOINTMENTS_PER_DAY = 20;

    // User Status Messages
    public static final String USER_CREATED_SUCCESSFULLY = "User created successfully";
    public static final String USER_UPDATED_SUCCESSFULLY = "User updated successfully";
    public static final String USER_DELETED_SUCCESSFULLY = "User deleted successfully";
    public static final String USER_NOT_FOUND = "User not found";
    public static final String EMAIL_ALREADY_EXISTS = "Email already exists";

    // Authentication Messages
    public static final String LOGIN_SUCCESSFUL = "Login successful";
    public static final String LOGOUT_SUCCESSFUL = "Logout successful";
    public static final String INVALID_CREDENTIALS = "Invalid email or password";
    public static final String ACCESS_DENIED = "Access denied";
    public static final String TOKEN_EXPIRED = "Token has expired";
    public static final String INVALID_TOKEN = "Invalid token";

    // Appointment Messages
    public static final String APPOINTMENT_BOOKED_SUCCESSFULLY = "Appointment booked successfully";
    public static final String APPOINTMENT_CANCELLED_SUCCESSFULLY = "Appointment cancelled successfully";
    public static final String APPOINTMENT_NOT_FOUND = "Appointment not found";
    public static final String APPOINTMENT_SLOT_NOT_AVAILABLE = "Appointment slot not available";

    // Doctor Messages
    public static final String DOCTOR_APPROVED_SUCCESSFULLY = "Doctor approved successfully";
    public static final String DOCTOR_REJECTED_SUCCESSFULLY = "Doctor rejected successfully";
    public static final String DOCTOR_NOT_FOUND = "Doctor not found";

    // Clinic Messages
    public static final String CLINIC_APPROVED_SUCCESSFULLY = "Clinic approved successfully";
    public static final String CLINIC_REJECTED_SUCCESSFULLY = "Clinic rejected successfully";
    public static final String CLINIC_NOT_FOUND = "Clinic not found";

    // Patient Messages
    public static final String PATIENT_NOT_FOUND = "Patient not found";

    // Diagnosis Messages
    public static final String DIAGNOSIS_CREATED_SUCCESSFULLY = "Diagnosis created successfully";
    public static final String DIAGNOSIS_NOT_FOUND = "Diagnosis not found";

    // Follow-up Messages
    public static final String FOLLOWUP_SCHEDULED_SUCCESSFULLY = "Follow-up scheduled successfully";
    public static final String FOLLOWUP_CANCELLED_SUCCESSFULLY = "Follow-up cancelled successfully";
    public static final String FOLLOWUP_NOT_FOUND = "Follow-up not found";

    // Announcement Messages
    public static final String ANNOUNCEMENT_CREATED_SUCCESSFULLY = "Announcement created successfully";
    public static final String ANNOUNCEMENT_UPDATED_SUCCESSFULLY = "Announcement updated successfully";
    public static final String ANNOUNCEMENT_DELETED_SUCCESSFULLY = "Announcement deleted successfully";
    public static final String ANNOUNCEMENT_NOT_FOUND = "Announcement not found";

    private Constants() {
        // Private constructor to prevent instantiation
    }
}
