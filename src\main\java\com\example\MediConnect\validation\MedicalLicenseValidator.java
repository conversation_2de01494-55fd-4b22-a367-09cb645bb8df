package com.example.MediConnect.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.regex.Pattern;

public class MedicalLicenseValidator implements ConstraintValidator<ValidMedicalLicense, String> {
    
    // Pattern for medical license: 2-3 letters followed by 4-8 digits
    private static final Pattern LICENSE_PATTERN = Pattern.compile("^[A-Z]{2,3}\\d{4,8}$");
    
    @Override
    public void initialize(ValidMedicalLicense constraintAnnotation) {
        // Initialization logic if needed
    }
    
    @Override
    public boolean isValid(String license, ConstraintValidatorContext context) {
        if (license == null || license.trim().isEmpty()) {
            return true; // Let @NotBlank handle null/empty validation
        }
        
        // Convert to uppercase and remove spaces
        String cleanedLicense = license.toUpperCase().replaceAll("\\s", "");
        
        return LICENSE_PATTERN.matcher(cleanedLicense).matches();
    }
}
