package com.example.MediConnect.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = MedicalLicenseValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidMedicalLicense {
    String message() default "Invalid medical license format";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
