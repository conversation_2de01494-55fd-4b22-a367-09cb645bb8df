package com.example.MediConnect.service;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import com.example.MediConnect.dto.request.LoginRequest;
import com.example.MediConnect.dto.request.RegisterRequest;
import com.example.MediConnect.dto.response.AuthResponse;
import com.example.MediConnect.entity.Admin;
import com.example.MediConnect.entity.Patient;
import com.example.MediConnect.entity.User;
import com.example.MediConnect.enums.Role;
import com.example.MediConnect.enums.UserStatus;
import com.example.MediConnect.exception.ResourceNotFoundException;
import com.example.MediConnect.repository.AdminRepository;
import com.example.MediConnect.repository.PatientRepository;
import com.example.MediConnect.repository.UserRepository;
import com.example.MediConnect.security.JwtUtil;
import com.example.MediConnect.service.impl.AuthServiceImpl;

@ExtendWith(MockitoExtension.class)
class AuthServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private AdminRepository adminRepository;

    @Mock
    private PatientRepository patientRepository;

    @Mock
    private JwtUtil jwtUtil;

    @Mock
    private BCryptPasswordEncoder passwordEncoder;

    @InjectMocks
    private AuthServiceImpl authService;

    private User testUser;
    private LoginRequest loginRequest;
    private RegisterRequest registerRequest;

    @BeforeEach
    void setUp() {
        testUser = new Admin();
        testUser.setId(1L);
        testUser.setName("Test Admin");
        testUser.setEmail("<EMAIL>");
        testUser.setPassword("encodedPassword");
        testUser.setRole(Role.ADMIN);
        testUser.setStatus(UserStatus.ACTIVE);
        testUser.setPhoneNumber("+**********");
        testUser.setAddress("123 Test St");
        testUser.setDateOfBirth("1990-01-01");
        testUser.setGender("MALE");

        loginRequest = new LoginRequest();
        loginRequest.setEmail("<EMAIL>");
        loginRequest.setPassword("password123");

        registerRequest = new RegisterRequest();
        registerRequest.setName("New Patient");
        registerRequest.setEmail("<EMAIL>");
        registerRequest.setPassword("password123");
        registerRequest.setRole(Role.PATIENT);
        registerRequest.setPhoneNumber("+**********");
        registerRequest.setAddress("456 Patient Ave");
        registerRequest.setDateOfBirth("1995-01-01");
        registerRequest.setGender("FEMALE");
        registerRequest.setBloodGroup("A+");
        registerRequest.setEmergencyContactName("Emergency Contact");
        registerRequest.setEmergencyContactPhone("+**********");
    }

    @Test
    void login_Success() {
        // Given
        when(userRepository.findByEmail("<EMAIL>")).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches("password123", "encodedPassword")).thenReturn(true);
        when(jwtUtil.generateToken("<EMAIL>", "ADMIN", 1L)).thenReturn("jwt-token");

        // When
        AuthResponse response = authService.login(loginRequest);

        // Then
        assertNotNull(response);
        assertEquals("jwt-token", response.getToken());
        assertEquals("<EMAIL>", response.getEmail());
        assertEquals("ADMIN", response.getRole());
        assertEquals(1L, response.getUserId());
        assertEquals("Test Admin", response.getName());
        assertEquals("Authentication successful", response.getMessage());

        verify(userRepository, times(1)).findByEmail("<EMAIL>");
        verify(passwordEncoder, times(1)).matches("password123", "encodedPassword");
        verify(jwtUtil, times(1)).generateToken("<EMAIL>", "ADMIN", 1L);
    }

    @Test
    void login_InvalidEmail() {
        // Given
        when(userRepository.findByEmail("<EMAIL>")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> {
            authService.login(loginRequest);
        });

        verify(userRepository, times(1)).findByEmail("<EMAIL>");
        verify(passwordEncoder, never()).matches(anyString(), anyString());
        verify(jwtUtil, never()).generateToken(anyString(), anyString(), any());
    }

    @Test
    void login_InvalidPassword() {
        // Given
        when(userRepository.findByEmail("<EMAIL>")).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches("password123", "encodedPassword")).thenReturn(false);

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> {
            authService.login(loginRequest);
        });

        verify(userRepository, times(1)).findByEmail("<EMAIL>");
        verify(passwordEncoder, times(1)).matches("password123", "encodedPassword");
        verify(jwtUtil, never()).generateToken(anyString(), anyString(), any());
    }

    @Test
    void register_PatientSuccess() {
        // Given
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
        when(passwordEncoder.encode("password123")).thenReturn("encodedPassword");
        when(jwtUtil.generateToken("<EMAIL>", "PATIENT", 2L)).thenReturn("jwt-token");

        Patient savedPatient = new Patient();
        savedPatient.setId(2L);
        savedPatient.setName("New Patient");
        savedPatient.setEmail("<EMAIL>");
        savedPatient.setRole(Role.PATIENT);

        when(patientRepository.save(any(Patient.class))).thenReturn(savedPatient);

        // When
        AuthResponse response = authService.register(registerRequest);

        // Then
        assertNotNull(response);
        assertEquals("jwt-token", response.getToken());
        assertEquals("<EMAIL>", response.getEmail());
        assertEquals("PATIENT", response.getRole());
        assertEquals(2L, response.getUserId());
        assertEquals("New Patient", response.getName());
        assertEquals("Authentication successful", response.getMessage());

        verify(userRepository, times(1)).existsByEmail("<EMAIL>");
        verify(passwordEncoder, times(1)).encode("password123");
        verify(patientRepository, times(1)).save(any(Patient.class));
        verify(jwtUtil, times(1)).generateToken("<EMAIL>", "PATIENT", 2L);
    }

    @Test
    void register_EmailAlreadyExists() {
        // Given
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            authService.register(registerRequest);
        });

        verify(userRepository, times(1)).existsByEmail("<EMAIL>");
        verify(passwordEncoder, never()).encode(anyString());
        verify(patientRepository, never()).save(any(Patient.class));
        verify(jwtUtil, never()).generateToken(anyString(), anyString(), any());
    }

    @Test
    void validateToken_Success() {
        // Given
        when(jwtUtil.validateToken("valid-token")).thenReturn(true);

        // When
        boolean result = authService.validateToken("valid-token");

        // Then
        assertTrue(result);
        verify(jwtUtil, times(1)).validateToken("valid-token");
    }

    @Test
    void validateToken_Invalid() {
        // Given
        when(jwtUtil.validateToken("invalid-token")).thenReturn(false);

        // When
        boolean result = authService.validateToken("invalid-token");

        // Then
        assertFalse(result);
        verify(jwtUtil, times(1)).validateToken("invalid-token");
    }
}
